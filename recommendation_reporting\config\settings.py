"""
Configuration settings for recommendation reporting.
"""
import os

# File and directory settings
DEFAULT_INPUT_DIR = "output_tables"
DEFAULT_OUTPUT_DIR = "output"
DEFAULT_TEMPLATE_DIR = "recommendation_reporting/templates"
TEMPLATE_FILE = "table_template.html"

# Processing settings
DEFAULT_MAX_WORKERS = 5
DEFAULT_LOCALE = "pt_BR"

# Logging settings
LOG_DIR = "logs"
LOG_FILE = "recommendation_report.log"

# Ensure log directory exists
os.makedirs(LOG_DIR, exist_ok=True)

# Product filtering thresholds
CUMULATIVE_SALES_HIGH_THRESHOLD = 0.1  # 10%
CUMULATIVE_SALES_LOW_THRESHOLD = 0.05   # 5%
TOP_PRODUCTS_FALLBACK = 3

# Currency formatting by locale
CURRENCY_FORMATS = {
    'pt_BR': 'R$',
    'es_AR': 'ARS',
    'pt': 'R$',
    'es': 'ARS'
}

# Column translations by locale
COLUMN_TRANSLATIONS = {
    'pt_BR': {
        'title': 'Descrição',
        'quality_score': 'Score de Qualidade',
        'item_id': 'MLB',
        'stock': 'Estoque',
        'position': 'Posição Mais Vendidos',
        'price': 'Preço',
        'abc_class': 'Curva A/B/C',
        'sales': 'Vendas',
        'conversion': 'Conversão',
        'sales_potential': 'Receita p/ Click'
    },
    'es_AR': {
        'title': 'Descripción',
        'quality_score': 'Puntaje de Calidad',
        'item_id': 'MLA',
        'stock': 'Stock',
        'position': 'Posición Más Vendidos',
        'price': 'Precio',
        'abc_class': 'Curva A/B/C',
        'sales': 'Ventas',
        'conversion': 'Conversión',
        'sales_potential': 'Ingresos p/ Click'
    }
}

# Page titles by locale
PAGE_TITLES = {
    'pt_BR': 'Recomendação de Produtos',
    'es_AR': 'Recomendación de Productos'
}

def get_currency_format(locale):
    """Get currency format for locale."""
    return CURRENCY_FORMATS.get(locale, 'R$')

def get_column_translations(locale):
    """Get column translations for locale."""
    return COLUMN_TRANSLATIONS.get(locale, COLUMN_TRANSLATIONS['pt_BR'])

def get_page_title(locale):
    """Get page title for locale."""
    return PAGE_TITLES.get(locale, PAGE_TITLES['pt_BR'])
