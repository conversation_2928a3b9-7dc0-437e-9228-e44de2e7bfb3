"""
Main Controller - Presentation Layer

Orchestrates the entire recommendation reporting process.
"""
import asyncio
import logging
import sys
import os
from typing import List

# Add the project root to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from pdf_helper import convert_html_to_pdf
from recommendation_reporting.data.file_service import (
    ensure_output_directory,
    get_csv_files,
    read_csv_file,
    get_merchant_output_path
)
from recommendation_reporting.business.recommendation_processor import process_product_data
from recommendation_reporting.business.report_generator import RecommendationReportGenerator

logger = logging.getLogger(__name__)


async def run_recommendation_reporting(locale: str = "pt_BR", input_dir: str = "output_tables",
                                     output_dir: str = "output", max_workers: int = 5):
    """
    Main entry point for recommendation reporting process.

    Args:
        locale: Language/locale for reports (default: pt_BR)
        input_dir: Directory containing CSV files to process
        output_dir: Directory for output files
        max_workers: Maximum number of concurrent workers
    """
    logger.info("Starting recommendation reporting process")

    # Setup environment
    ensure_output_directory(output_dir)

    # Get CSV files to process
    csv_files = get_csv_files(input_dir)
    if not csv_files:
        logger.warning(f"No CSV files found in {input_dir}")
        return

    # Initialize report generator
    report_generator = RecommendationReportGenerator()

    # Create semaphore for concurrent processing
    semaphore = asyncio.Semaphore(max_workers)

    # Process files concurrently
    tasks = [
        process_single_file(semaphore, file, input_dir, output_dir, locale, report_generator)
        for file in csv_files
    ]

    results = await asyncio.gather(*tasks, return_exceptions=True)

    # Log results
    successful = 0
    failed = 0

    for i, result in enumerate(results):
        if isinstance(result, Exception):
            logger.error(f"Failed to process {csv_files[i]}: {result}")
            failed += 1
        else:
            logger.info(result)
            if isinstance(result, str) and "Success" in result:
                successful += 1
            else:
                failed += 1

    logger.info(f"Recommendation reporting completed. Processed {successful} files successfully, {failed} failed")


async def process_single_file(semaphore: asyncio.Semaphore, filename: str, input_dir: str,
                            output_dir: str, locale: str, report_generator: RecommendationReportGenerator) -> str:
    """
    Process a single CSV file and generate recommendation report.

    Args:
        semaphore: Semaphore for concurrency control
        filename: Name of the CSV file to process
        input_dir: Input directory
        output_dir: Output directory
        locale: Locale for the report
        report_generator: Report generator instance

    Returns:
        Status message
    """
    try:
        # Read input file
        file_path = os.path.join(input_dir, filename)
        df = await read_csv_file(file_path)

        # Extract store information
        if 'store_name' not in df.columns or 'store_permalink' not in df.columns:
            return f"Error processing {filename}: Missing store_name or store_permalink columns"

        store_name = df['store_name'].iloc[0]
        store_permalink = df['store_permalink'].iloc[0]

        # Process product data
        df_recommended, df_others = process_product_data(df, locale)

        # Generate HTML report (for PDF conversion only, no HTML file saved)
        html_output = report_generator.generate_html_report(
            df_recommended, df_others, store_name, store_permalink, locale
        )

        # Generate output filename and path - extract merchant from filename
        merchant_name = filename.split("__")[0].removesuffix(".csv")

        # Use merchant-specific directory for organized output (following campaign reporting pattern)
        merchant_output_dir = get_merchant_output_path(merchant_name, output_dir)
        pdf_filename = f'Relatório Ads {merchant_name}.pdf'
        merchant_pdf_path = os.path.join(merchant_output_dir, pdf_filename)

        # Convert to PDF with semaphore control (no HTML file saved)
        async with semaphore:
            success = await convert_html_to_pdf(html_output, merchant_pdf_path)

        if success:
            return f"Processed {filename} - Success (saved to {merchant_pdf_path})"
        else:
            return f"Processed {filename} - Failed (PDF conversion failed)"

    except Exception as e:
        logger.error(f"Error processing {filename}: {e}")
        return f"Error processing {filename}: {str(e)}"
