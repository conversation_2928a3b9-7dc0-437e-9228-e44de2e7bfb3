"""
Recommendation Processor - Business Layer

Handles the core business logic for product recommendations.
"""
import logging
import pandas as pd
from typing import Tuple
from recommendation_reporting.config.settings import (
    CUMULATIVE_SALES_HIGH_THRESHOLD,
    CUMULATIVE_SALES_LOW_THRESHOLD,
    TOP_PRODUCTS_FALLBACK,
    get_currency_format,
    get_column_translations
)

logger = logging.getLogger(__name__)


def filter_products_by_sales(df: pd.DataFrame) -> pd.DataFrame:
    """
    Filter and categorize products based on sales performance.
    
    Args:
        df: DataFrame with product data
        
    Returns:
        DataFrame with product_group column added
    """
    # Calculate the cumulative sales percentage
    df['cumulative_sales'] = df['sales'] / df['sales'].sum()

    # Initialize product groups
    df['product_group'] = -1
    df.loc[df['sales'] >= 1, 'product_group'] = 1

    # First, try to recommend products that represent more than 10% of sales
    if df[df['cumulative_sales'] >= CUMULATIVE_SALES_HIGH_THRESHOLD].shape[0] > 0:
        df.loc[df['cumulative_sales'] >= CUMULATIVE_SALES_HIGH_THRESHOLD, 'product_group'] = 2
    # If not, recommend products that represent more than 5% of sales
    elif df[df['cumulative_sales'] >= CUMULATIVE_SALES_LOW_THRESHOLD].shape[0] > 0:
        df.loc[df['cumulative_sales'] >= CUMULATIVE_SALES_LOW_THRESHOLD, 'product_group'] = 2
    # If not, recommend the top 3 products
    else:
        df_sorted = df.sort_values(by='sales', ascending=False)
        top_indices = df_sorted.head(TOP_PRODUCTS_FALLBACK).index
        df.loc[top_indices, 'product_group'] = 2

    logger.info(f"Categorized products: {len(df[df['product_group'] == 2])} recommended, "
                f"{len(df[df['product_group'] == 1])} others")
    
    return df


def format_dataframe_for_display(df: pd.DataFrame, locale: str) -> pd.DataFrame:
    """
    Format DataFrame columns for display.
    
    Args:
        df: DataFrame to format
        locale: Locale for formatting
        
    Returns:
        Formatted DataFrame
    """
    df_formatted = df.copy()
    
    # Format conversion as percentage
    if 'conversion' in df_formatted.columns:
        df_formatted['conversion'] = (df_formatted['conversion']).astype(float).map('{:.1%}'.format)

    # Format currency columns
    currency_symbol = get_currency_format(locale)
    
    if 'price' in df_formatted.columns:
        df_formatted['price'] = df_formatted['price'].apply(lambda x: f"{currency_symbol}{x:,.2f}")
    
    if 'sales_potential' in df_formatted.columns:
        df_formatted['sales_potential'] = df_formatted['sales_potential'].apply(lambda x: f"{currency_symbol}{x:,.2f}")

    # Format stock column
    if 'stock' in df_formatted.columns:
        df_formatted['stock'] = df_formatted['stock'].fillna(0).astype(int)

    # Format position and quality_score columns
    if 'position' in df_formatted.columns:
        df_formatted['position'] = df_formatted['position'].fillna('-')
    
    if 'quality_score' in df_formatted.columns:
        df_formatted['quality_score'] = df_formatted['quality_score'].fillna('-')

    logger.debug(f"Formatted DataFrame for locale: {locale}")
    return df_formatted


def select_and_rename_columns(df: pd.DataFrame, locale: str) -> pd.DataFrame:
    """
    Select relevant columns and rename them according to locale.
    
    Args:
        df: DataFrame to process
        locale: Locale for column names
        
    Returns:
        DataFrame with selected and renamed columns
    """
    # Select desired columns
    columns_to_select = [
        'permalink', 'image_url', 'title', 'quality_score', 'item_id', 
        'stock', 'position', 'price', 'abc_class', 'sales', 'conversion', 'sales_potential'
    ]
    
    # Filter to only existing columns
    existing_columns = [col for col in columns_to_select if col in df.columns]
    df_selected = df[existing_columns].copy()
    
    # Get translations for locale
    translations = get_column_translations(locale)
    
    # Rename columns
    rename_mapping = {col: translations.get(col, col) for col in df_selected.columns if col in translations}
    df_renamed = df_selected.rename(columns=rename_mapping)
    
    # Remove position column if all values are '-'
    position_col = translations.get('position', 'position')
    if position_col in df_renamed.columns and (df_renamed[position_col] == '-').all():
        df_renamed = df_renamed.drop(columns=[position_col])
        logger.debug(f"Removed {position_col} column (all values were '-')")
    
    logger.debug(f"Selected and renamed columns for locale: {locale}")
    return df_renamed


def process_product_data(df: pd.DataFrame, locale: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Process product data and return recommended and other products.
    
    Args:
        df: Raw product DataFrame
        locale: Locale for formatting
        
    Returns:
        Tuple of (recommended_products_df, other_products_df)
    """
    # Filter products by sales
    df_filtered = filter_products_by_sales(df)
    
    # Format for display
    df_formatted = format_dataframe_for_display(df_filtered, locale)
    
    # Split into recommended and other products
    df_recommended = df_formatted[df_formatted['product_group'] == 2].copy()
    df_others = df_formatted[df_formatted['product_group'] == 1].copy()
    
    # Sort others by sales and sales potential
    df_others = df_others.sort_values(by=['sales', 'sales_potential'], ascending=False)
    
    # Select and rename columns
    df_recommended = select_and_rename_columns(df_recommended, locale)
    df_others = select_and_rename_columns(df_others, locale)
    
    logger.info(f"Processed product data: {len(df_recommended)} recommended, {len(df_others)} others")
    
    return df_recommended, df_others
