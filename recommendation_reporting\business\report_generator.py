"""
Report Generator - Business Layer

Handles HTML report generation from processed data.
"""
import logging
import os
from jinja2 import Environment, FileSystemLoader
from recommendation_reporting.config.settings import (
    DEFAULT_TEMPLATE_DIR,
    TEMPLATE_FILE,
    get_page_title
)

logger = logging.getLogger(__name__)


class RecommendationReportGenerator:
    """Generates HTML reports for product recommendations."""
    
    def __init__(self, template_dir: str = None):
        """
        Initialize the report generator.
        
        Args:
            template_dir: Directory containing templates (optional)
        """
        self.template_dir = template_dir or DEFAULT_TEMPLATE_DIR
        self.env = Environment(loader=FileSystemLoader(self.template_dir))
        logger.debug(f"Initialized RecommendationReportGenerator with template_dir: {self.template_dir}")
    
    def generate_html_report(self, df_recommended, df_others, store_name: str, 
                           store_permalink: str, locale: str) -> str:
        """
        Generate HTML report from processed data.
        
        Args:
            df_recommended: DataFrame with recommended products
            df_others: DataFrame with other products
            store_name: Name of the store
            store_permalink: Store permalink URL
            locale: Locale for the report
            
        Returns:
            Generated HTML content as string
        """
        try:
            template = self.env.get_template(TEMPLATE_FILE)
            page_title = get_page_title(locale)
            
            html_output = template.render(
                df_rec=df_recommended,
                df_others=df_others,
                store_name=store_name,
                store_permalink=store_permalink,
                page_title_text=page_title,
                locale=locale
            )
            
            logger.debug(f"Generated HTML report for store: {store_name}")
            return html_output
            
        except Exception as e:
            logger.error(f"Error generating HTML report for store {store_name}: {e}")
            raise


def get_tutorial_url(store_permalink: str, locale: str) -> str:
    """
    Get tutorial URL based on store permalink and locale.
    
    Args:
        store_permalink: Store permalink URL
        locale: Locale for the tutorial
        
    Returns:
        Tutorial URL
    """
    try:
        # Extract country code from permalink
        country_code = store_permalink.split('/')[-2].split('.')[-1]
        
        if locale.startswith('pt'):
            return f'https://vendedores.mercadolivre.com.{country_code}/nota/como-criar-campanhas-publicitarias-no-product-ads'
        else:
            return f'https://vendedores.mercadolibre.com.{country_code}/nota/como-crear-nuevas-campanas-en-product-ads'
            
    except Exception as e:
        logger.warning(f"Could not extract country code from {store_permalink}: {e}")
        # Fallback URLs
        if locale.startswith('pt'):
            return 'https://vendedores.mercadolivre.com.br/nota/como-criar-campanhas-publicitarias-no-product-ads'
        else:
            return 'https://vendedores.mercadolibre.com.ar/nota/como-crear-nuevas-campanas-en-product-ads'
