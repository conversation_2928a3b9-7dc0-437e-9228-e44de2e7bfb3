"""
<PERSON><PERSON><PERSON> to move the campaign files from the recent run to merchant directories.
"""
import sys
import os
import shutil

# Add the current directory to the path so we can import modules
sys.path.append(os.path.dirname(__file__))

from campaign_reporting.data.file_service import get_merchant_output_path, ensure_merchant_directory

def move_campaign_files():
    """Move campaign files to merchant directories."""
    print("🚀 Moving Campaign Files to Merchant Directories")
    print("=" * 60)
    
    # Files from the recent campaign run
    merchant_id = "6294bb10ce2ebe00011b543e"
    
    files_to_move = [
        "campaign_metrics_Lista_Gerencial_6294bb10ce2ebe00011b543e_344532343.html",
        "campaign_metrics_Lista_Gerencial_6294bb10ce2ebe00011b543e_344532343.pdf",
        "campaign_metrics_TESTE_ACOS_10_6294bb10ce2ebe00011b543e_352570006.html",
        "campaign_metrics_TESTE_ACOS_10_6294bb10ce2ebe00011b543e_352570006.pdf"
    ]
    
    # Ensure merchant directory exists
    merchant_dir = ensure_merchant_directory(merchant_id)
    print(f"📁 Merchant directory: {merchant_dir}")
    
    moved_count = 0
    
    for filename in files_to_move:
        source_path = os.path.join("output_pdf", filename)
        
        if os.path.exists(source_path):
            target_path = get_merchant_output_path(merchant_id, filename)
            
            try:
                shutil.move(source_path, target_path)
                file_size = os.path.getsize(target_path)
                print(f"✅ Moved: {filename} ({file_size:,} bytes)")
                moved_count += 1
            except Exception as e:
                print(f"❌ Error moving {filename}: {e}")
        else:
            print(f"⚠️  File not found: {source_path}")
    
    print(f"\n📊 Summary: {moved_count}/{len(files_to_move)} files moved successfully")
    
    # Also move WhatsApp reports from output_campaigns to merchant directory
    print(f"\n📱 Moving WhatsApp reports...")
    
    whatsapp_files = [
        "6294bb10ce2ebe00011b543e__344532343.txt",
        "6294bb10ce2ebe00011b543e__352570006.txt"
    ]
    
    whatsapp_moved = 0
    
    for filename in whatsapp_files:
        source_path = os.path.join("output_campaigns", filename)
        
        if os.path.exists(source_path):
            target_path = get_merchant_output_path(merchant_id, filename)
            
            try:
                shutil.copy2(source_path, target_path)  # Copy instead of move to preserve original
                file_size = os.path.getsize(target_path)
                print(f"✅ Copied: {filename} ({file_size:,} bytes)")
                whatsapp_moved += 1
            except Exception as e:
                print(f"❌ Error copying {filename}: {e}")
        else:
            print(f"⚠️  File not found: {source_path}")
    
    print(f"\n📊 WhatsApp Summary: {whatsapp_moved}/{len(whatsapp_files)} files copied successfully")
    
    # Show final merchant directory contents
    print(f"\n📂 Final merchant directory contents:")
    if os.path.exists(merchant_dir):
        files = os.listdir(merchant_dir)
        total_size = 0
        
        for file in sorted(files):
            file_path = os.path.join(merchant_dir, file)
            file_size = os.path.getsize(file_path)
            total_size += file_size
            
            # Determine file type
            if file.endswith('.html'):
                file_type = "📄 HTML"
            elif file.endswith('.pdf'):
                file_type = "📕 PDF"
            elif file.endswith('.txt'):
                file_type = "📱 WhatsApp"
            elif file.endswith('.csv'):
                file_type = "📊 CSV"
            else:
                file_type = "📁 File"
            
            print(f"   {file_type}: {file} ({file_size:,} bytes)")
        
        print(f"\n📈 Total: {len(files)} files, {total_size:,} bytes")
    
    print(f"\n🎉 Campaign files organization completed!")
    print(f"📍 All files for merchant {merchant_id} are now in: {merchant_dir}")

if __name__ == "__main__":
    move_campaign_files()
