"""
Unified MercadoLibre API client with dependency injection.

This module combines the API client and service layers into a single, efficient client
that uses dependency injection for access tokens and provides both low-level API access
and high-level service methods.
"""
import aiohttp
import asyncio
import re
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union

import pandas as pd
import config
from logging_config import get_logger

logger = get_logger(__name__)


class MercadoLibreClient:
    """Unified MercadoLibre API client with dependency injection and batch processing."""

    def __init__(self, access_token: str, user_id: int, request_delay: float = 0.1):
        """Initialize the client with dependency injection.

        Args:
            access_token: MercadoLibre API access token
            user_id: User ID for API calls
            request_delay: Delay in seconds between requests to prevent rate limiting (default: 0.1s)
        """
        self.access_token = access_token
        self.user_id = str(user_id)
        self.base_url = config.MELI_API_BASE_URL
        self.session = None
        self.request_delay = request_delay

    async def __aenter__(self):
        """Set up the aiohttp session when entering the context manager."""
        if not self.session:
            self.session = aiohttp.ClientSession(
                headers={"Authorization": f"Bearer {self.access_token}"}
            )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Close the aiohttp session when exiting the context manager."""
        if self.session:
            await self.session.close()
            self.session = None

    async def cleanup(self):
        """Clean up the session."""
        if self.session:
            await self.session.close()
            self.session = None

    async def _ensure_session(self):
        """Ensure the session is initialized."""
        if not self.session:
            self.session = aiohttp.ClientSession(
                headers={"Authorization": f"Bearer {self.access_token}"}
            )

    async def _make_single_request(self, method: str, endpoint: str, retry_count: int = 0, **kwargs) -> Any:
        """Make a single API request with error handling and exponential backoff."""
        await self._ensure_session()
        url = f"{self.base_url}{endpoint}"

        try:
            async with getattr(self.session, method.lower())(url, **kwargs) as response:
                if response.status == 429:  # Rate limited
                    if retry_count >= 3:  # Max 3 retries
                        raise Exception("Rate limit exceeded after 3 retries")

                    retry_after = int(response.headers.get("Retry-After", 1))
                    # Add exponential backoff: base delay + exponential component
                    backoff_delay = retry_after + (2 ** retry_count)
                    logger.warning(f"Rate limited. Waiting for {backoff_delay} seconds (retry {retry_count + 1}/3)")
                    await asyncio.sleep(backoff_delay)
                    return await self._make_single_request(method, endpoint, retry_count + 1, **kwargs)

                if response.status == 401:  # Unauthorized
                    error_text = await response.text()
                    logger.error(f"Unauthorized: {error_text}")
                    raise Exception("Unauthorized: Access token is invalid or expired")

                if response.status >= 400:
                    error_text = await response.text()
                    logger.error(f"API error: {response.status} - {error_text}")
                    raise Exception(f"API error: {response.status} - {error_text}")

                # Add delay before returning to prevent rate limiting
                if self.request_delay > 0:
                    await asyncio.sleep(self.request_delay)

                return await response.json()
        except aiohttp.ClientError as e:
            logger.error(f"Request error: {str(e)}")
            raise Exception(f"Request error: {str(e)}") from e

    async def _make_request(self, method: str, endpoint: str, paginate: bool = False,
                           pagination_key: str = "results", page_size: int = 50,
                           limit: Optional[int] = None, **kwargs) -> Any:
        """Make an API request with optional pagination.

        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint to call
            paginate: Whether to handle pagination automatically
            pagination_key: The key in the response that contains the items to paginate
            page_size: Number of items per page
            limit: Maximum number of items to return (None for all)
            **kwargs: Additional arguments to pass to the request

        Returns:
            API response or list of paginated results
        """
        # If not paginating, make a simple request
        if not paginate:
            return await self._make_single_request(method, endpoint, **kwargs)

        # Handle pagination
        all_results = []
        offset = 0
        total_items = None

        # Fetch all pages of results
        while True:
            # Update pagination parameters in the endpoint
            current_endpoint = endpoint
            if "offset=" in current_endpoint:
                # Replace existing offset and limit parameters
                current_endpoint = re.sub(r'offset=\d+', f'offset={offset}', current_endpoint)
                current_endpoint = re.sub(r'limit=\d+', f'limit={page_size}', current_endpoint)
            else:
                # Add pagination parameters with current offset value
                if "?" in current_endpoint:
                    current_endpoint += f"&offset={offset}&limit={page_size}"
                else:
                    current_endpoint += f"?offset={offset}&limit={page_size}"

            # Make the request for this page
            response = await self._make_single_request(method, current_endpoint, **kwargs)

            # Get paging information
            paging = response.get("paging", {})
            if total_items is None:
                total_items = paging.get("total", 0)
                logger.info(f"Found {total_items} total items")

                # If a limit is specified, adjust total_items
                if limit is not None and limit < total_items:
                    total_items = limit
                    logger.info(f"Limiting results to {limit} items")

            # Add results from this page
            page_results = response.get(pagination_key, [])
            all_results.extend(page_results)

            # Check if we've reached the limit or the end of results
            if limit is not None and len(all_results) >= limit:
                all_results = all_results[:limit]  # Trim to exact limit
                break

            # Check if we've fetched all pages
            if offset + page_size >= paging.get("total", 0) or not page_results:
                break

            # Move to the next page
            offset += page_size
            logger.debug(f"Fetching next page, offset: {offset}")

        return all_results

    # ========================================
    # HIGH-LEVEL SERVICE METHODS
    # ========================================

    # Catalog methods
    async def get_all_items(self) -> List[str]:
        """Fetch all items from the user's account."""
        endpoint = f"/users/{self.user_id}/items/search"
        return await self._make_request(
            "GET",
            endpoint,
            paginate=True,
            pagination_key="results"
        )

    async def get_active_listings(self, limit: Optional[int] = None, count_only: bool = False) -> Union[List[Dict], int]:
        """Get all active listings for the user or just the count."""
        endpoint = f"/users/{self.user_id}/items/search?status=active"

        if count_only:
            response = await self._make_single_request("GET", f"{endpoint}&limit=1")
            total = response.get("paging", {}).get("total", 0)
            logger.info(f"Total active listings: {total}")
            return total

        item_ids = await self._make_request(
            "GET",
            endpoint,
            paginate=True,
            pagination_key="results",
            limit=limit
        )

        logger.info(f"Fetching details for {len(item_ids)} active listings")
        result = await self.get_item_details(item_ids)
        return result if isinstance(result, list) else [result]

    async def get_inactive_listings(self, limit: Optional[int] = None, count_only: bool = False) -> Union[List[Dict], int]:
        """Get all inactive listings for the user or just the count."""
        endpoint = f"/users/{self.user_id}/items/search?status=paused,closed"

        if count_only:
            response = await self._make_single_request("GET", f"{endpoint}&limit=1")
            total = response.get("paging", {}).get("total", 0)
            logger.info(f"Total inactive listings: {total}")
            return total

        item_ids = await self._make_request(
            "GET",
            endpoint,
            paginate=True,
            pagination_key="results",
            limit=limit
        )

        logger.info(f"Fetching details for {len(item_ids)} inactive listings")
        result = await self.get_item_details(item_ids)
        return result if isinstance(result, list) else [result]

    async def get_new_listings(self, days: int = 7, limit: Optional[int] = None, count_only: bool = False) -> Union[List[Dict], int]:
        """Get listings created in the last X days or just the count."""
        since_date = datetime.now() - timedelta(days=days)
        since_str = since_date.strftime("%Y-%m-%dT%H:%M:%S.000Z")
        endpoint = f"/users/{self.user_id}/items/search?date_from={since_str}"

        if count_only:
            response = await self._make_single_request("GET", f"{endpoint}&limit=1")
            total = response.get("paging", {}).get("total", 0)
            logger.info(f"Total new listings in the last {days} days: {total}")
            return total

        item_ids = await self._make_request(
            "GET",
            endpoint,
            paginate=True,
            pagination_key="results",
            limit=limit
        )

        logger.info(f"Fetching details for {len(item_ids)} new listings from the last {days} days")
        result = await self.get_item_details(item_ids)
        return result if isinstance(result, list) else [result]

    async def get_item_details(self, item_id: Union[str, List[str]]) -> Union[Dict, List[Dict]]:
        """Get detailed information about one or multiple items."""
        # Handle single item case
        if isinstance(item_id, str):
            endpoint = f"/items/{item_id}"
            return await self._make_request("GET", endpoint)

        # Handle batch/multiget case
        if not item_id:  # Empty list
            return []

        # Process in batches of 20 (API limitation for multiget endpoint)
        all_results = []
        batch_size = 20

        for i in range(0, len(item_id), batch_size):
            batch = item_id[i:i+batch_size]

            # Construct the multiget endpoint with comma-separated IDs
            ids_param = ",".join(batch)
            endpoint = f"/items?ids={ids_param}"

            # Make the multiget request
            response = await self._make_request("GET", endpoint)

            # Process the multiget response format
            for item_response in response:
                if item_response.get("code") == 200:
                    all_results.append(item_response.get("body", {}))
                else:
                    # Log error but don't fail the entire batch
                    error_msg = item_response.get("body", {}).get("message", "Unknown error")
                    item_id_with_error = item_response.get("body", {}).get("id", "unknown")
                    logger.warning(f"Error fetching item {item_id_with_error}: {error_msg}")
                    # Add a placeholder with error information
                    all_results.append({
                        "id": item_id_with_error,
                        "error": error_msg,
                        "code": item_response.get("code")
                    })

        return all_results

    async def get_item_description(self, item_id: str) -> Dict:
        """Get the description of a specific item."""
        endpoint = f"/items/{item_id}/description"
        return await self._make_request("GET", endpoint)

    async def get_item_visits(self, item_id: str) -> Dict:
        """Get visit statistics for an item."""
        endpoint = f"/visits/items?ids={item_id}"
        return await self._make_request("GET", endpoint)

    # Account metrics methods
    async def get_account_metrics(self) -> Dict:
        """Get overall account metrics from user data."""
        endpoint = f"/users/{self.user_id}"
        return await self._make_request("GET", endpoint)

    async def get_shipping_metrics(self) -> Dict:
        """Get shipping performance metrics."""
        endpoint = f"/users/{self.user_id}/shipping_metrics"
        return await self._make_request("GET", endpoint)

    async def get_sales_metrics(self, period: str = "last_week") -> Dict:
        """Get sales metrics for a specific period."""
        endpoint = f"/users/{self.user_id}/sales_metrics?period={period}"
        return await self._make_request("GET", endpoint)

    async def get_claims(self, status: str = "all") -> List[Dict]:
        """Get claims/complaints for the seller."""
        endpoint = f"/users/{self.user_id}/claims?status={status}"
        return await self._make_request("GET", endpoint)

    async def get_cancellations(self) -> List[Dict]:
        """Get order cancellations."""
        endpoint = f"/users/{self.user_id}/cancellations"
        return await self._make_request("GET", endpoint)

    async def get_messages_metrics(self) -> Dict:
        """Get messaging response metrics."""
        endpoint = f"/messages/metrics/{self.user_id}"
        return await self._make_request("GET", endpoint)

    # Category methods
    async def get_category_attributes(self, category_id: str) -> List[Dict]:
        """Get attributes for a specific category."""
        endpoint = f"/categories/{category_id}/attributes"
        return await self._make_request("GET", endpoint)

    async def get_category_trends(self, category_id: str) -> List[Dict]:
        """Get trending keywords for a specific category."""
        # The trends endpoint uses the site ID (MLB for Brazil) followed by the category ID
        site_id = category_id[:3] if len(category_id) > 3 else "MLB"
        endpoint = f"/trends/{site_id}/{category_id}"
        try:
            return await self._make_request("GET", endpoint)
        except Exception as e:
            logger.warning(f"Error fetching trends for category {category_id}: {str(e)}")
            return []

    # Quality methods
    async def get_item_health(self, item_id: str) -> Dict:
        """Get health metrics for a specific item."""
        endpoint = f"/items/{item_id}/health"
        return await self._make_request("GET", endpoint)

    async def get_account_tags(self) -> List[Dict]:
        """Get all tags associated with the seller account."""
        endpoint = f"/users/{self.user_id}/tags"
        response = await self._make_request("GET", endpoint)
        return response.get("tags", [])

    async def get_item_purchase_experience(self, item_id: str) -> Dict:
        """Get purchase experience data for a specific item."""
        endpoint = f"/reputation/items/{item_id}/purchase_experience/integrators"
        params = {"locale": "pt-BR"}
        return await self._make_request("GET", endpoint, params=params)

    async def get_batch_purchase_experience(self, item_ids: List[str]) -> List[Dict]:
        """Get purchase experience data for multiple items efficiently."""
        if not item_ids:
            return []

        # Process items concurrently for better performance
        import asyncio

        async def fetch_single_experience(item_id: str) -> Dict:
            try:
                return await self.get_item_purchase_experience(item_id)
            except Exception as e:
                logger.error(f"Error fetching purchase experience for item {item_id}: {str(e)}")
                return {"item_id": item_id, "error": str(e)}

        # Use semaphore to limit concurrent requests (avoid overwhelming the API)
        semaphore = asyncio.Semaphore(5)  # Max 5 concurrent requests (reduced for better rate limiting)

        async def fetch_with_semaphore(item_id: str) -> Dict:
            async with semaphore:
                return await fetch_single_experience(item_id)

        # Execute all requests concurrently
        tasks = [fetch_with_semaphore(item_id) for item_id in item_ids]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Filter out exceptions and return valid results
        valid_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Exception for item {item_ids[i]}: {str(result)}")
                valid_results.append({"item_id": item_ids[i], "error": str(result)})
            else:
                valid_results.append(result)

        return valid_results

    # ========================================
    # ADVERTISING AND CAMPAIGN METHODS
    # ========================================

    async def get_advertiser_id_pads(self) -> Optional[str]:
        """Get advertiser ID for PADS product."""
        endpoint = "/advertising/advertisers?product_id=PADS"
        headers = {
            "Content-Type": "application/json",
            "Api-Version": "1"
        }

        try:
            response = await self._make_single_request("GET", endpoint, headers=headers)
            if response and response.get("advertisers"):
                return response["advertisers"][0]['advertiser_id']
            return None
        except Exception as e:
            logger.error(f"Error fetching advertiser ID: {str(e)}")
            return None

    async def fetch_campaign_data(self, campaign_id: str, date_from: str, date_to: str) -> Optional[Dict]:
        """Fetch campaign data with comprehensive metrics.

        Args:
            campaign_id: Campaign ID to fetch data for
            date_from: Start date in YYYY-MM-DD format
            date_to: End date in YYYY-MM-DD format

        Returns:
            Campaign data dictionary or None if error
        """
        metrics_fields = (
            "clicks,prints,ctr,cost,cpc,acos,organic_units_quantity,organic_units_amount,"
            "organic_items_quantity,direct_items_quantity,indirect_items_quantity,"
            "advertising_items_quantity,cvr,roas,sov,direct_units_quantity,indirect_units_quantity,"
            "units_quantity,direct_amount,indirect_amount,total_amount,impression_share,"
            "top_impression_share,lost_impression_share_by_budget,lost_impression_share_by_ad_rank,"
            "acos_benchmark"
        )

        endpoint = (
            f"/advertising/product_ads/campaigns/{campaign_id}"
            f"?date_from={date_from}&date_to={date_to}&metrics={metrics_fields}"
        )

        headers = {"Api-Version": "2"}

        try:
            return await self._make_single_request("GET", endpoint, headers=headers)
        except Exception as e:
            logger.error(f"Failed to fetch campaign data for campaign {campaign_id}: {str(e)}")
            return None

    async def list_advertiser_campaigns(self, advertiser_id: str, date_from: str, date_to: str,
                                       limit: int = 50) -> Optional[List[Dict]]:
        """List campaigns for an advertiser.

        Args:
            advertiser_id: Advertiser ID
            date_from: Start date in YYYY-MM-DD format
            date_to: End date in YYYY-MM-DD format
            limit: Number of items per page

        Returns:
            List of campaigns or None if error
        """
        campaigns = []
        headers = {"Api-Version": "2"}
        offset = 0

        while True:
            endpoint = (
                f"/advertising/advertisers/{advertiser_id}/product_ads/campaigns"
                f"?date_from={date_from}&date_to={date_to}&metrics=cost&offset={offset}&limit={limit}"
            )

            try:
                response = await self._make_single_request("GET", endpoint, headers=headers)

                if not response:
                    break

                results = response.get("results", [])
                if not results:
                    break

                campaigns.extend(results)

                paging = response.get("paging", {})
                total = paging.get("total", 0)

                offset_next = offset + limit
                if offset_next >= total:
                    break
                offset = offset_next

                # Add a small delay between requests to avoid hitting rate limits
                await asyncio.sleep(0.5)

            except Exception as e:
                logger.error(f"Error listing campaigns for advertiser {advertiser_id}: {str(e)}")
                return None

        logger.info(f"Found {len(campaigns)} campaigns for advertiser {advertiser_id}")
        return campaigns

    async def list_seller_items_unlimited(self, status: str = "active", limit: int = 50) -> List[str]:
        """List seller items without limit using scroll scan.

        Args:
            status: Item status filter (active, paused, closed, etc.)
            limit: Number of items per page

        Returns:
            List of item IDs
        """
        logger.info(f"Listing items for seller {self.user_id} (scroll scan) with status={status}...")

        all_item_ids = []
        scroll_id = None

        while True:
            if scroll_id:
                endpoint = (
                    f"/users/{self.user_id}/items/search"
                    f"?status={status}&search_type=scan&scroll_id={scroll_id}&limit={limit}"
                )
            else:
                endpoint = (
                    f"/users/{self.user_id}/items/search"
                    f"?status={status}&search_type=scan&limit={limit}"
                )

            try:
                response = await self._make_single_request("GET", endpoint)

                if not response:
                    logger.warning("Failed to fetch items with scan.")
                    break

                results = response.get("results", [])
                if not results:
                    logger.info("No additional results found.")
                    break

                all_item_ids.extend(results)

                new_scroll_id = response.get("scroll_id", None)
                if not new_scroll_id or new_scroll_id == scroll_id:
                    logger.info("Scroll pagination exhausted (or no new scroll_id).")
                    break

                scroll_id = new_scroll_id
                logger.debug(f"+{len(results)} items. Partial total: {len(all_item_ids)}.")

            except Exception as e:
                logger.error(f"Error during scroll scan: {str(e)}")
                break

        logger.info(f"Final total of {len(all_item_ids)} items collected (via scroll scan).")
        return all_item_ids

    async def list_product_ads_items(self, advertiser_id: str, campaign_id: str,
                                   date_from: str, date_to: str, limit: int = 50) -> List[Dict]:
        """List product ads items with metrics by date.

        Args:
            advertiser_id: Advertiser ID
            campaign_id: Campaign ID to filter by
            date_from: Start date in YYYY-MM-DD format
            date_to: End date in YYYY-MM-DD format
            limit: Number of items per page

        Returns:
            List of product ads items with metrics
        """
        all_ads_items = []
        metrics_fields = (
            "clicks,prints,ctr,cost,cpc,acos,"
            "units_quantity,direct_units_quantity,indirect_units_quantity,"
            "direct_items_quantity,indirect_items_quantity,"
            "organic_units_quantity,organic_items_quantity,advertising_items_quantity,"
            "cvr,roas,sov,direct_amount,indirect_amount,total_amount"
        )

        headers = {"Api-Version": "2"}
        date_range = pd.date_range(start=date_from, end=date_to)

        for single_date in date_range:
            date_str = single_date.strftime("%Y-%m-%d")
            logger.info(f"Collecting data for date: {date_str}")

            offset = 0
            while True:
                endpoint = (
                    f"/advertising/advertisers/{advertiser_id}/product_ads/items"
                    f"?limit={limit}&offset={offset}&date_from={date_str}&date_to={date_str}"
                    f"&metrics={metrics_fields}&filters[campaign_id]={campaign_id}"
                )

                try:
                    response = await self._make_single_request("GET", endpoint, headers=headers)

                    if not response:
                        break

                    results = response.get("results", [])
                    if not results:
                        break

                    # Add date to each item
                    for item in results:
                        item["date"] = date_str
                    all_ads_items.extend(results)

                    paging = response.get("paging", {})
                    total = paging.get("total", 0)
                    offset_next = offset + limit

                    if offset_next >= total:
                        break
                    offset = offset_next

                except Exception as e:
                    logger.error(f"Error fetching product ads items: {str(e)}")
                    raise

        logger.info(f"Total of {len(all_ads_items)} ads returned.")
        return all_ads_items

    async def get_product_sales_by_date(self, date_from: str, date_to: str) -> Dict[str, Dict[str, int]]:
        """Get product sales organized by product ID and date.

        Args:
            date_from: Start date in ISO format (YYYY-MM-DD)
            date_to: End date in ISO format (YYYY-MM-DD)

        Returns:
            Dictionary with product IDs as keys and nested dictionaries of dates and sales counts
            Example: {
                'MLB123456': {
                    '2025-04-01': 3,
                    '2025-04-02': 1,
                    ...
                },
                ...
            }
        """
        # First, we need to import the get_all_items_with_sales function
        # For now, we'll implement the core logic directly

        endpoint = "/orders/search"
        params = {
            'seller': self.user_id,
            'order.status': 'paid',
            'order.date_created.from': date_from,
            'order.date_created.to': date_to,
            'limit': 50,
        }

        # Initialize dictionary to store sales by product and date
        product_sales_by_date = {}
        offset = 0

        while True:
            params['offset'] = offset
            try:
                response = await self._make_single_request("GET", endpoint, params=params)

                if not response:
                    break

                results = response.get('results', [])
                if not results:
                    break

                for order in results:
                    if order.get("order_items") and len(order["order_items"]) > 0:
                        item_id = order["order_items"][0]["item"]["id"]
                        # Extract date from order creation timestamp (YYYY-MM-DD)
                        order_date = order["date_created"].split('T')[0]

                        # Initialize product entry if it doesn't exist
                        if item_id not in product_sales_by_date:
                            product_sales_by_date[item_id] = {}

                        # Increment the count for this item on this date
                        if order_date in product_sales_by_date[item_id]:
                            product_sales_by_date[item_id][order_date] += 1
                        else:
                            product_sales_by_date[item_id][order_date] = 1

                # Check pagination
                paging = response.get('paging', {})
                total = paging.get('total', 0)
                offset_next = offset + 50

                if offset_next >= total:
                    break
                offset = offset_next

            except Exception as e:
                logger.error(f"Error fetching product sales by date: {str(e)}")
                break

        logger.info(f"Found sales data for {len(product_sales_by_date)} products")
        return product_sales_by_date
