from PIL import Image, ImageDraw, ImageFont
import textwrap

def create_whatsapp_screenshot():
    # Create blank image with WhatsApp background color
    width, height = 400, 700
    background_color = (229, 221, 213)  # WhatsApp background color
    img = Image.new('RGB', (width, height), background_color)
    draw = ImageDraw.Draw(img)
    
    # Font settings
    try:
        font = ImageFont.truetype("arial.ttf", 16)
        time_font = ImageFont.truetype("arial.ttf", 12)
        name_font = ImageFont.truetype("arialbd.ttf", 18)
    except:
        print("Font not found, using default")
        font = ImageFont.load_default()
        time_font = ImageFont.load_default()
        name_font = ImageFont.load_default()
    
    # Draw header
    header_color = (0, 128, 105)  # WhatsApp green
    draw.rectangle([(0, 0), (width, 70)], fill=header_color)
    draw.text((15, 35), "John Doe", font=name_font, fill="white")
    
    # Chat messages
    messages = [
        {"text": "Hey, did you see the new movie?", "sender": "friend", "time": "10:30 AM"},
        {"text": "Not yet! Is it good?", "sender": "me", "time": "10:31 AM"},
        {"text": "Absolutely! You should watch it this weekend.", "sender": "friend", "time": "10:32 AM"},
        {"text": "Sure, let's go together? 🎬", "sender": "me", "time": "10:33 AM"},
        {"text": "Great idea! I'll book tickets for Saturday.", "sender": "friend", "time": "10:35 AM"},
        {"text": "Perfect! 😊", "sender": "me", "time": "10:36 AM"},
    ]
    
    # Starting position for messages
    y_position = 80
    
    for msg in messages:
        # Text wrapping
        wrapped = textwrap.wrap(msg["text"], width=30)
        text_height = len(wrapped) * 20
        
        # Bubble dimensions
        bubble_height = text_height + 30
        bubble_width = min(width - 100, 300)
        
        # Bubble positioning
        if msg["sender"] == "me":
            bubble_color = (220, 248, 198)  # Light green
            x_pos = width - bubble_width - 20
            text_pos = (x_pos + 10, y_position + 15)
            time_pos = (x_pos + bubble_width - 40, y_position + text_height + 15)
        else:
            bubble_color = (255, 255, 255)  # White
            x_pos = 20
            text_pos = (x_pos + 10, y_position + 15)
            time_pos = (x_pos + bubble_width - 40, y_position + text_height + 15)
        
        # Draw bubble
        draw.rounded_rectangle(
            [(x_pos, y_position), (x_pos + bubble_width, y_position + bubble_height)],
            radius=10,
            fill=bubble_color
        )
        
        # Draw text
        for i, line in enumerate(wrapped):
            draw.text((text_pos[0], text_pos[1] + i*20), line, font=font, fill="black")
        
        # Draw time
        draw.text(time_pos, msg["time"], font=time_font, fill=(100, 100, 100))
        
        # Update position for next message
        y_position += bubble_height + 20
    
    # Save and show image
    img.save("whatsapp_screenshot.png")
    img.show()

if __name__ == "__main__":
    create_whatsapp_screenshot()