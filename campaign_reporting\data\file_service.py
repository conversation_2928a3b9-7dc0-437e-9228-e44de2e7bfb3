"""
File Service

Handles all file I/O operations.
"""
import os
import pandas as pd
import logging
from campaign_reporting.config.settings import OUTPUT_CAMPAIGNS_DIR

logger = logging.getLogger(__name__)


def ensure_output_directory():
    """Ensure the main output directory exists."""
    # Create main output directory
    os.makedirs(OUTPUT_CAMPAIGNS_DIR, exist_ok=True)


def ensure_merchant_directory(merchant: str):
    """
    Ensure the merchant-specific directory exists.

    Args:
        merchant: Merchant identifier

    Returns:
        Path to the merchant directory
    """
    merchant_dir = os.path.join("output", merchant)
    os.makedirs(merchant_dir, exist_ok=True)
    logger.info(f"Ensured merchant directory: {merchant_dir}")
    return merchant_dir



def write_whatsapp_report(content: str, merchant: str, campaign_id: str):
    """
    Write WhatsApp report to merchant-specific directory.

    Args:
        content: Report content
        merchant: Merchant identifier
        campaign_id: Campaign ID
    """
    # Ensure merchant directory exists
    merchant_dir = ensure_merchant_directory(merchant)

    # Create report file in merchant directory
    report_file_path = os.path.join(merchant_dir, f'{merchant}__{campaign_id}.txt')

    try:
        with open(report_file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        logger.info(f"WhatsApp report written to {report_file_path}")
    except Exception as e:
        logger.error(f"Error writing WhatsApp report: {e}")


def append_campaign_to_csv(campaign_data, merchant: str):
    """
    Append campaign data to both global and merchant-specific CSV files.

    Args:
        campaign_data: Campaign data to append
        merchant: Merchant identifier
    """
    try:
        # Normalize campaign data
        df = pd.json_normalize(campaign_data, sep='_')
        if not df.empty:
            # Add user ID column to identify which user this data belongs to
            df['user_id'] = merchant
            df['merchant_id'] = merchant

            # 1. Append to global CSV file (for backward compatibility)
            file_exists = os.path.isfile(OUTPUT_CSV_FILE)
            df.to_csv(OUTPUT_CSV_FILE, mode='a', header=not file_exists, index=False)

            # 2. Save to merchant-specific CSV file
            merchant_dir = ensure_merchant_directory(merchant)
            merchant_csv_path = os.path.join(merchant_dir, f'{merchant}_campaigns.csv')

            # Check if merchant CSV exists
            merchant_file_exists = os.path.isfile(merchant_csv_path)
            df.to_csv(merchant_csv_path, mode='a', header=not merchant_file_exists, index=False)

            logger.info(f"Campaign data saved to global CSV and {merchant_csv_path}")
        else:
            logger.warning(f"Empty dataframe for merchant {merchant}")
    except Exception as e:
        logger.error(f"Error saving campaign data: {e}")


def get_merchant_output_path(merchant: str, filename: str):
    """
    Get the full path for a file in the merchant's output directory.

    Args:
        merchant: Merchant identifier
        filename: Name of the file

    Returns:
        Full path to the file in merchant directory
    """
    merchant_dir = ensure_merchant_directory(merchant)
    return os.path.join(merchant_dir, filename)


def clean_merchant_directory(merchant: str):
    """
    Clean all files in a merchant's directory.

    Args:
        merchant: Merchant identifier
    """
    merchant_dir = os.path.join("output", merchant)
    if os.path.exists(merchant_dir):
        try:
            for filename in os.listdir(merchant_dir):
                file_path = os.path.join(merchant_dir, filename)
                if os.path.isfile(file_path):
                    os.remove(file_path)
            logger.info(f"Cleaned merchant directory: {merchant_dir}")
        except Exception as e:
            logger.error(f"Error cleaning merchant directory {merchant_dir}: {e}")


def setup_cache_directory():
    """
    Set up cache directory for API requests.

    Returns:
        Path to cache directory
    """
    cache_dir = os.path.join(os.getcwd(), 'cache')
    os.makedirs(cache_dir, exist_ok=True)
    logger.info(f"Cache directory set up at: {cache_dir}")
    return cache_dir
