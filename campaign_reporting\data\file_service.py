"""
File Service

Handles all file I/O operations.
"""
import os
import pandas as pd
import logging
from campaign_reporting.config.settings import OUTPUT_CAMPAIGNS_DIR, OUTPUT_CSV_FILE

logger = logging.getLogger(__name__)


def ensure_output_directory():
    """Ensure the main output directory exists."""
    # Create main output directory
    os.makedirs(OUTPUT_CAMPAIGNS_DIR, exist_ok=True)


def ensure_merchant_directory(merchant: str):
    """
    Ensure the merchant-specific directory exists.

    Args:
        merchant: Merchant identifier

    Returns:
        Path to the merchant directory
    """
    merchant_dir = os.path.join("output", merchant)
    os.makedirs(merchant_dir, exist_ok=True)
    logger.info(f"Ensured merchant directory: {merchant_dir}")
    return merchant_dir


def ensure_merchant_report_directories(merchant: str):
    """
    Ensure all report-specific directories exist for a merchant.

    Args:
        merchant: Merchant identifier

    Returns:
        Dictionary with paths to each report type directory
    """
    merchant_dir = ensure_merchant_directory(merchant)

    # Create subdirectories for different report types
    whatsapp_dir = os.path.join(merchant_dir, "whatsapp_reports")
    performance_dir = os.path.join(merchant_dir, "performance_reports")
    recommendations_dir = os.path.join(merchant_dir, "recommendations")

    # Ensure all directories exist
    os.makedirs(whatsapp_dir, exist_ok=True)
    os.makedirs(performance_dir, exist_ok=True)
    os.makedirs(recommendations_dir, exist_ok=True)

    logger.info(f"Ensured report directories for merchant {merchant}")

    return {
        'base': merchant_dir,
        'whatsapp': whatsapp_dir,
        'performance': performance_dir,
        'recommendations': recommendations_dir
    }



def write_whatsapp_report(content: str, merchant: str, campaign_id: str):
    """
    Write WhatsApp report to merchant-specific WhatsApp reports directory.

    Args:
        content: Report content
        merchant: Merchant identifier
        campaign_id: Campaign ID
    """
    # Ensure merchant report directories exist
    report_dirs = ensure_merchant_report_directories(merchant)

    # Create report file in WhatsApp reports directory
    report_file_path = os.path.join(report_dirs['whatsapp'], f'{merchant}__{campaign_id}.txt')

    try:
        with open(report_file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        logger.info(f"WhatsApp report written to {report_file_path}")
    except Exception as e:
        logger.error(f"Error writing WhatsApp report: {e}")


def append_campaign_to_csv(campaign_data, merchant: str):
    """
    Append campaign data to both global and merchant-specific CSV files.

    Args:
        campaign_data: Campaign data to append
        merchant: Merchant identifier
    """
    try:
        # Normalize campaign data
        df = pd.json_normalize(campaign_data, sep='_')
        if not df.empty:
            # Add user ID column to identify which user this data belongs to
            df['user_id'] = merchant
            df['merchant_id'] = merchant

            # 2. Save to merchant-specific CSV file
            merchant_dir = ensure_merchant_directory(merchant)
            merchant_csv_path = os.path.join(merchant_dir, f'{merchant}_campaigns.csv')

            # Check if merchant CSV exists
            merchant_file_exists = os.path.isfile(merchant_csv_path)
            df.to_csv(merchant_csv_path, mode='a', header=not merchant_file_exists, index=False)

            logger.info(f"Campaign data saved to global CSV and {merchant_csv_path}")
        else:
            logger.warning(f"Empty dataframe for merchant {merchant}")
    except Exception as e:
        logger.error(f"Error saving campaign data: {e}")


def get_merchant_output_path(merchant: str, filename: str):
    """
    Get the full path for a file in the merchant's output directory.

    Args:
        merchant: Merchant identifier
        filename: Name of the file

    Returns:
        Full path to the file in merchant directory
    """
    merchant_dir = ensure_merchant_directory(merchant)
    return os.path.join(merchant_dir, filename)


def get_merchant_performance_report_path(merchant: str, filename: str):
    """
    Get the full path for a performance report file in the merchant's performance reports directory.

    Args:
        merchant: Merchant identifier
        filename: Name of the file

    Returns:
        Full path to the file in merchant's performance reports directory
    """
    report_dirs = ensure_merchant_report_directories(merchant)
    return os.path.join(report_dirs['performance'], filename)


def get_merchant_recommendation_report_path(merchant: str, filename: str):
    """
    Get the full path for a recommendation report file in the merchant's recommendations directory.

    Args:
        merchant: Merchant identifier
        filename: Name of the file

    Returns:
        Full path to the file in merchant's recommendations directory
    """
    report_dirs = ensure_merchant_report_directories(merchant)
    return os.path.join(report_dirs['recommendations'], filename)


def clean_merchant_directory(merchant: str):
    """
    Clean all files in a merchant's directory.

    Args:
        merchant: Merchant identifier
    """
    merchant_dir = os.path.join("output", merchant)
    if os.path.exists(merchant_dir):
        try:
            for filename in os.listdir(merchant_dir):
                file_path = os.path.join(merchant_dir, filename)
                if os.path.isfile(file_path):
                    os.remove(file_path)
            logger.info(f"Cleaned merchant directory: {merchant_dir}")
        except Exception as e:
            logger.error(f"Error cleaning merchant directory {merchant_dir}: {e}")


def setup_cache_directory():
    """
    Set up cache directory for API requests.

    Returns:
        Path to cache directory
    """
    cache_dir = os.path.join(os.getcwd(), 'cache')
    os.makedirs(cache_dir, exist_ok=True)
    logger.info(f"Cache directory set up at: {cache_dir}")
    return cache_dir
