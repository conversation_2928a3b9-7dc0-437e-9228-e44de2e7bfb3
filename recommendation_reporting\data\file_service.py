"""
File Service - Data Layer

Handles file operations for recommendation reporting.
"""
import os
import io
import logging
import aiofiles
import pandas as pd
from typing import List, Tuple

logger = logging.getLogger(__name__)


def ensure_output_directory(output_dir: str):
    """Ensure output directory exists."""
    os.makedirs(output_dir, exist_ok=True)
    logger.info(f"Output directory ensured: {output_dir}")


def get_csv_files(input_dir: str) -> List[str]:
    """
    Get list of CSV files from input directory.
    
    Args:
        input_dir: Directory to scan for CSV files
        
    Returns:
        List of CSV filenames
    """
    if not os.path.exists(input_dir):
        logger.error(f"Input directory does not exist: {input_dir}")
        return []
    
    files = [
        f for f in os.listdir(input_dir)
        if f.endswith('.csv') and os.path.isfile(os.path.join(input_dir, f))
    ]
    
    logger.info(f"Found {len(files)} CSV files in {input_dir}")
    return files


async def read_csv_file(file_path: str) -> pd.DataFrame:
    """
    Read CSV file asynchronously.
    
    Args:
        file_path: Path to CSV file
        
    Returns:
        DataFrame with CSV data
    """
    try:
        async with aiofiles.open(file_path, mode='r', encoding="utf-8") as f:
            content = await f.read()
            df = pd.read_csv(io.StringIO(content))
            logger.debug(f"Successfully read CSV file: {file_path}")
            return df
    except Exception as e:
        logger.error(f"Error reading CSV file {file_path}: {e}")
        raise


def generate_output_filename(input_filename: str, output_dir: str) -> Tuple[str, str]:
    """
    Generate output filename from input filename.
    
    Args:
        input_filename: Original CSV filename
        output_dir: Output directory
        
    Returns:
        Tuple of (merchant_name, full_pdf_path)
    """
    # Extract merchant name from filename (before first __)
    merchant_name = input_filename.split("__")[0].removesuffix(".csv")
    pdf_filename = f'Relatório Ads {merchant_name}.pdf'
    pdf_path = os.path.join(output_dir, pdf_filename)
    
    return merchant_name, pdf_path


def get_merchant_output_path(merchant_name: str, output_dir: str) -> str:
    """
    Get output path for a specific merchant.
    
    Args:
        merchant_name: Name of the merchant
        output_dir: Base output directory
        
    Returns:
        Path to merchant-specific output directory
    """
    merchant_dir = os.path.join(output_dir, merchant_name)
    os.makedirs(merchant_dir, exist_ok=True)
    return merchant_dir
