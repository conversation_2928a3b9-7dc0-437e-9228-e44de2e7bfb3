from input_data import get_go_bots_api_response
from recommendation_report import filter_input
from pdf_helper import convert_html_to_pdf
from services.mercadolibre_service import MercadoLibreClient
from services.gobots_service import filter_merchants_data
import os
import asyncio
import logging
from datetime import datetime, timedelta
from input_data import build_output, calculate_metrics
from jinja2 import Environment, FileSystemLoader
from aiohttp_client_cache.session import CachedSession
from aiohttp_client_cache.backends.filesystem import FileBackend

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler("logs/script_campaign_recommend.log"), logging.StreamHandler()],
)

logger = logging.getLogger()


DATE_FORMAT = "%Y-%m-%d"
DATE_TO = datetime.now().strftime(DATE_FORMAT)
DATE_FROM = (datetime.now() - timedelta(days=30)).strftime(DATE_FORMAT)



async def recommend_non_advertised_items_and_items_to_remove(session, merchant, seller_id, go_bots_data):
    logger.info(f"Processing merchant {merchant}, seller {seller_id}")

    # Ensure go_bots_data is a list
    if not isinstance(go_bots_data, list):
        logger.error(f"Invalid go_bots_data type: {type(go_bots_data)}")
        return

    # Use the efficient search function from gobots_service
    filters = {
        'merchant_id': merchant,
        'seller_id': str(seller_id)
    }

    matches = filter_merchants_data(go_bots_data, filters)

    if not matches:
        logger.error(f"No access token found for merchant {merchant}, seller {seller_id}")
        return

    # Get the first match (there should only be one for a specific merchant+seller combination)
    access_token = matches[0]['access_token']
    logger.info(f"Found access token for merchant {merchant}, seller {seller_id}")

    # Extract user_id from access_token for MercadoLibreClient
    user_id = access_token.split('-')[-1] if '-' in access_token else '1'

    async with MercadoLibreClient(access_token, int(user_id)) as client:
        logger.info(f"Fetching advertiser ID for user {merchant}")
        advertiser_id = await client.get_advertiser_id_pads()
        if not advertiser_id:
            logger.error(f"[ERRO] Não foi possível obter advertiser de PADS. Abortando {merchant}.")
            return

        # 1) Listar campanhas
        logger.info(f"Listing campaigns for user {merchant} (advertiser {advertiser_id})")
        campaigns = await client.list_advertiser_campaigns(advertiser_id, DATE_FROM, DATE_TO)

        if not campaigns:
            logger.error(f"[ERRO] Nenhuma campanha encontrada {merchant}.")
            return

        campaign_products = []

        for campaign in campaigns:
            logger.info(f"Fetching data for campaign {campaign['id']} (user {merchant})")
            if campaign['date_created'].split("-")[0] == '2025':
                campaign['date_created'] = DATE_FROM
            campaign_data = await client.fetch_campaign_data(campaign['id'], campaign['date_created'].split("T")[0], DATE_TO)
            if (campaign_data and
                campaign_data.get('metrics') and
                campaign_data.get('metrics', {}).get("clicks")):
                logger.info(f"Listing product ads items for campaign {campaign['id']} (user {merchant})")
                this_campaign_products = await client.list_product_ads_items(advertiser_id, campaign['id'], campaign['date_created'].split("T")[0], DATE_TO)
                campaign_products.extend(this_campaign_products)

    # calculate bad performing products
    # logger.info(f"Calculating bad performing products for user {merchant}")
    # for campaign in campaign_products:
    #     print(campaign)

    logger.info(f"Building output data for user {merchant}")
    df = await build_output(session, seller_id, access_token, 30)
    if df.shape[0] > 0:
        df = calculate_metrics(df)
        df['quality_score'] = df['quality_score'].astype('Int64')
        df['position'] = df['position'].astype('Int64')
        df = filter_input(df)
        df_rec = df[df['product_group'] == 2]

        # Start with initial max_head value
        max_head = 3
        # Get list of items already in campaigns
        campaign_item_ids = [campaign['item_id'] for campaign in campaign_products]

        # Initialize unlisted_recommended_items as empty
        unlisted_recommended_items = set()

        # Minimum number of items we want to find
        min_items_required = 3

        # Initialize attempts counter
        attempts = 0

        # df_rec but remove if sales_potential is nan
        df_rec = df_rec[~df_rec['sales_potential'].isna()]

        if len(df_rec['item_id'].tolist()) > min_items_required:
            unlisted_recommended_items = df_rec['item_id'].tolist()

        else:
        # Keep doubling max_head until we find enough unlisted recommended items or reach all A-class items
            max_attempts = 5  # Limit the number of attempts to avoid infinite loop
            a_class_items_count = df[df['abc_class'] == "A"].shape[0]

            while (len(unlisted_recommended_items) < min_items_required and
                attempts < max_attempts and
                max_head <= a_class_items_count):
                logger.info(f"Trying with max_head = {max_head} for user {merchant}")
                top_abc_items = df[df['abc_class'] == "A"].head(max_head)
                recommended_items = df_rec['item_id'].tolist() + top_abc_items['item_id'].tolist()
                unlisted_recommended_items = set(recommended_items) - set(campaign_item_ids)

                logger.info(f"Found {len(unlisted_recommended_items)} unlisted items, need at least {min_items_required}")

                if len(unlisted_recommended_items) < min_items_required:
                    max_head *= 2  # Double max_head for next attempt
                    attempts += 1

        if unlisted_recommended_items:
            max_recommended_items = 8
            unlisted_recommended_items = list(unlisted_recommended_items)[:max_recommended_items]
            logger.info(f"Found {len(unlisted_recommended_items)} unlisted recommended items with max_head = {max_head}")
            df_unlisted = df[df['item_id'].isin(unlisted_recommended_items)]
            await create_recommendation_report(df_unlisted, merchant)

            # Log a warning if we couldn't find the minimum required number of items
            if len(unlisted_recommended_items) < min_items_required:
                logger.warning(f"Only found {len(unlisted_recommended_items)} items, which is less than the minimum required ({min_items_required})")
        else:
            logger.warning(f"Could not find any unlisted recommended items after {attempts} attempts")
            # Create an empty DataFrame with the same structure to show the "no products found" message
            if not df.empty:
                empty_df = df.head(0)
                empty_df.loc[0, 'store_name'] = df['store_name'].iloc[0]
                empty_df.loc[0, 'store_permalink'] = df['store_permalink'].iloc[0]
                await create_recommendation_report(empty_df, merchant)


async def create_recommendation_report(df_unlisted, merchant):
    # Get store information
    store_name = df_unlisted['store_name'].iloc[0]
    store_permalink = df_unlisted['store_permalink'].iloc[0]

    country_code = store_permalink.split('/')[-2].split('.')[-1]
    tutorial_url = f'https://vendedores.mercadolibre.com.{country_code}/nota/como-crear-nuevas-campanas-en-product-ads'

    # Format numeric columns for display if they exist (make a copy to avoid warnings)
    df_unlisted = df_unlisted.copy()
    if 'price' in df_unlisted.columns:
        df_unlisted['price'] = df_unlisted['price'].astype(float)
    if 'sales_potential' in df_unlisted.columns:
        df_unlisted['sales_potential'] = df_unlisted['sales_potential'].astype(float)
    if 'conversion' in df_unlisted.columns:
        df_unlisted['conversion'] = df_unlisted['conversion'].astype(float)

    # Set up Jinja2 environment
    env = Environment(loader=FileSystemLoader('.'))
    template = env.get_template('produtos_recomendados.html')

    # Render HTML
    html_output = template.render(
        df_unlisted=df_unlisted,
        store_name=store_name,
        store_permalink=store_permalink,
        tutorial_url=tutorial_url
    )

    # Create output directory if it doesn't exist
    os.makedirs('output_campaigns_test', exist_ok=True)

    # Generate filenames
    sanitized_store_name = store_name.replace(" ", "_").replace("/", "_")
    date_str = datetime.now().strftime("%Y%m%d")
    pdf_path = f'output_campaigns_test/produtos_recomendados_{sanitized_store_name}_{merchant}_{date_str}.pdf'

    # Convert to PDF
    success = await convert_html_to_pdf(html_output, pdf_path)

    if success:
        logger.info(f"Generated recommendation report for user {merchant} at {pdf_path}")
    else:
        logger.error(f"Failed to generate PDF for user {merchant}")


async def main():
    os.makedirs('output_campaigns_test', exist_ok=True)

    # Set up cache directory
    cache_dir = os.path.join(os.getcwd(), 'cache')
    os.makedirs(cache_dir, exist_ok=True)

    # Configure the cache with a 1-hour expiration time (3600 seconds)
    logger.info(f"Setting up cache in directory: {cache_dir} with 1-hour expiration")

    # Create a file cache with 1-hour expiration
    cache = FileBackend(
        cache_name=cache_dir,
        expire_after=3600,
        allowed_methods=('GET', 'POST'),  # Cache both GET and POST requests
        include_headers=True,             # Include headers in the cache key
        ignored_params=['access_token'],  # Don't include access tokens in cache keys for security
        cache_control=True                # Respect Cache-Control headers from the server
    )

    with open('merchants.txt', 'r') as f:
        merchants_ids = [(uid.strip()) for uid in f.read().split(',')]

    # Create a cached session with additional options
    async with CachedSession(
        cache=cache,
        retry_429=True,                   # Automatically retry on rate limit errors
        retry_backoff_factor=0.5,         # Start with a 0.5s delay, then 1s, 2s, etc.
        retry_max_retries=5               # Maximum number of retries
    ) as session:
        logger.info("Using cached session with FileBackend (expires=3600s)")

        go_bots_data = await get_go_bots_api_response(session)
        if not go_bots_data:
            logger.error("Failed to fetch GoBots data")
            return

        # Ensure go_bots_data is a list
        if not isinstance(go_bots_data, list):
            logger.error(f"Invalid go_bots_data type: {type(go_bots_data)}")
            return

        # Process each merchant and their sellers
        tasks = []
        for merchant_id in merchants_ids:
            logger.info(f"Processing merchant: {merchant_id}")

            # Use the efficient search function from gobots_service
            filters = {'merchant_id': merchant_id}
            merchant_matches = filter_merchants_data(go_bots_data, filters)

            if merchant_matches:
                logger.info(f"Found {len(merchant_matches)} sellers for merchant {merchant_id}")
                # Process each seller for this merchant
                for match in merchant_matches:
                    seller_id = match['seller_id']
                    tasks.append(recommend_non_advertised_items_and_items_to_remove(session, merchant_id, seller_id, go_bots_data))
            else:
                logger.warning(f"No sellers found for merchant {merchant_id}")

        if tasks:
            await asyncio.gather(*tasks)
        else:
            logger.warning("No tasks to process")



if __name__ == "__main__":
    asyncio.run(main())
