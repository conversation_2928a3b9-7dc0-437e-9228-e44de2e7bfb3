"""Repository layer for campaign recommendations."""
import asyncio
from datetime import datetime
from aiohttp_client_cache import CachedSession
from utils.logger import get_logger
from services.mercadolibre_service import MercadoLibreClient
from input_data import build_output
from services.gobots_service import GoBotsService

logger = get_logger(__name__)

class CampaignRepository:
    def __init__(self, session: CachedSession):
        self.session = session
        self.gobots_service = GoBotsService()

    async def get_access_token(self, user_id: int):
        return await self.gobots_service.get_user_access_token(user_id)

    async def get_advertiser_id(self, access_token, user_id):
        async with MercadoLibreClient(access_token, int(user_id)) as client:
            return await client.get_advertiser_id_pads()

    async def get_campaigns(self, access_token, user_id, advertiser_id, date_from, date_to):
        async with MercadoLibreClient(access_token, int(user_id)) as client:
            return await client.list_advertiser_campaigns(advertiser_id, date_from, date_to)

    async def get_campaign_products(self, access_token, user_id, advertiser_id, campaigns, date_from, date_to):
        campaign_products = []
        async with MercadoLibreClient(access_token, int(user_id)) as client:
            for campaign in campaigns:
                if campaign['date_created'].split("-")[0] == '2025':
                    campaign['date_created'] = date_from
                campaign_data = await client.fetch_campaign_data(
                    campaign['id'],
                    campaign['date_created'].split("T")[0],
                    date_to
                )
                if campaign_data and campaign_data.get('metrics') and campaign_data.get('metrics').get("clicks"):
                    this_campaign_products = await client.list_product_ads_items(
                        advertiser_id,
                        campaign['id'],
                        campaign['date_created'].split("T")[0],
                        date_to
                    )
                    campaign_products.extend(this_campaign_products)
        return campaign_products

    async def get_product_data(self, uid, access_token, days=30):
        return await build_output(self.session, uid, access_token, days)
