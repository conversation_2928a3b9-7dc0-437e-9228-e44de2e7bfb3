<!DOCTYPE html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{page_title_text}}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="text-center">
        <img src="https://gobots.ai/wp-content/uploads/2022/02/logo-gobots-solucao-platinum-mercado-livre-inteligencia-artificial-cor.png" alt="Centered Image" class="img-fluid"> 
    </div>
    <div class="container mt-5"></div>
      <h1 class="text-center">
        {% if locale and locale.startswith('es') %}
          Informe para campaña de Product Ads
        {% else %}
          Relatório para campanha de Product Ads
        {% endif %}
      </h1>
    <div class="container mt-5">        
        <h2>
          {% if locale and locale.startswith('es') %}
            Lista de productos para <a href={{store_permalink}}>{{store_name}}</a>
          {% else %}
            Lista de produtos para <a href={{store_permalink}}>{{store_name}}</a>
          {% endif %}
        </h2>
        <p>
          {% if locale and locale.startswith('es') %}
            Los siguientes productos se dividen entre los productos recomendados para una campaña de Product Ads y todos los demás productos que tuvieron al menos una venta en el período considerado (últimos 30 días).
          {% else %}
            Os produtos a seguir são divididos entre os produtos recomendados para uma campanha de Product Ads e todos os outros produtos que tiveram pelo menos uma venda no período considerado (últimos 30 dias).
          {% endif %}
        </p>
        <p>
          {% if locale and locale.startswith('es') %}
            Mercado Libre tiene un <a href="https://vendedores.mercadolibre.com.ar/nota/como-crear-nuevas-campanas-en-product-ads">tutorial explicativo</a> sobre cuáles son los pasos necesarios para crear una campaña de Product Ads. Nuestra sugerencia para el ACOS es entre 3% y 8%
          {% else %}
            O Mercado Livre possui um <a href="https://vendedores.mercadolivre.com.br/nota/como-criar-campanhas-publicitarias-no-product-ads">tutorial explicativo</a> sobre quais são os passos necessários para a criação de uma campanha de Product Ads. A nossa sugestão para o ACOS é entre 3% e 8%
          {% endif %}
        </p>
    </div>
    <div class="container mt-5">  
        <h3>
          {% if locale and locale.startswith('es') %}
            Productos sugeridos para campañas de Ads
          {% else %}
            Produtos sugeridos para campanhas de Ads
          {% endif %}
        </h3>
        <table class="table">
            <thead class="table-dark">
              <tr>
                <th class="text-center align-middle">
                  {% if locale and locale.startswith('es') %}
                    Producto
                  {% else %}
                    Produto
                  {% endif %}
                </th>
                {% for column in df_rec.columns %}
                    {% if column != 'image_url' and column != 'permalink'%}
                        <th class="text-center align-middle">{{ column }}</th>
                    {% endif %}
                {% endfor %}
              </tr>
            </thead>
            <tbody>
                {% for _, row in df_rec.iterrows() %}
                  <tr>
                    <td class="text-center align-middle">
                        <a href="{{ row['permalink'] }}">
                          <img src="{{ row['image_url'] }}" alt="Product Image" class="img-fluid" style="max-width: 100px;">
                        </a>
                      </td>
                    {% for column, value in row.items() %}
                        {% if column != 'image_url' and column != 'permalink'%}
                            <td class="text-center align-middle">                                
                                {{ value }}                                
                            </td>
                        {% endif %}
                    {% endfor %}
                  </tr>
                {% endfor %}
              </tbody>
        </table>
    </div>
    <div class="container mt-5">
      <h3>
        {% if locale and locale.startswith('es') %}
          Otros productos con ventas en los últimos 30 días
        {% else %}
          Outros produtos com vendas nos últimos 30 dias
        {% endif %}
      </h3>
      <table class="table">
          <thead class="table-dark">
            <tr>
              <th class="text-center align-middle">
                {% if locale and locale.startswith('es') %}
                  Producto
                {% else %}
                  Produto
                {% endif %}
              </th>
              {% for column in df_others.columns %}
                  {% if column != 'image_url' and column != 'permalink'%}
                      <th class="text-center align-middle">{{ column }}</th>
                  {% endif %}
              {% endfor %}
            </tr>
          </thead>
          <tbody>
              {% for _, row in df_others.iterrows() %}
                <tr>
                  <td class="text-center align-middle">
                      <a href="{{ row['permalink'] }}">
                        <img src="{{ row['image_url'] }}" alt="Product Image" class="img-fluid" style="max-width: 100px;">
                      </a>
                    </td>
                  {% for column, value in row.items() %}
                      {% if column != 'image_url' and column != 'permalink'%}
                          <td class="text-center align-middle">                                
                              {{ value }}                                
                          </td>
                      {% endif %}
                  {% endfor %}
                </tr>
              {% endfor %}
            </tbody>
      </table>
  </div>
</body>
</html>
