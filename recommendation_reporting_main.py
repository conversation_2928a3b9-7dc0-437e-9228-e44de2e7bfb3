"""
Recommendation Reporting - Layered Architecture Entry Point

This is the main entry point for the refactored recommendation reporting system.
"""
import asyncio
import sys
import os
import argparse

# Add the current directory to the path so we can import modules
sys.path.append(os.path.dirname(__file__))

import logging_config
from recommendation_reporting.presentation.main_controller import run_recommendation_reporting

# Setup logging
logger = logging_config.setup_logging(__name__)


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Recommendation Reporting System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python recommendation_reporting_main.py --locale pt_BR --input_dir output_tables
  python recommendation_reporting_main.py --locale es_AR --input_dir output_tables --output_dir custom_output
        """
    )

    parser.add_argument(
        '--locale',
        type=str,
        default='pt_BR',
        choices=['pt_BR', 'es_AR', 'pt', 'es'],
        help='Locale for the reports (default: pt_BR). Accepts pt_BR, es_AR, pt, or es'
    )

    parser.add_argument(
        '--input_dir',
        type=str,
        default='output_tables',
        help='Directory containing CSV files to process (default: output_tables)'
    )

    parser.add_argument(
        '--output_dir',
        type=str,
        default='output',
        help='Directory for output PDF files (default: output)'
    )

    parser.add_argument(
        '--max_workers',
        type=int,
        default=5,
        help='Maximum number of concurrent workers (default: 5)'
    )

    return parser.parse_args()


async def main():
    """Main entry point for the recommendation reporting application."""
    try:
        # Parse command line arguments
        args = parse_arguments()

        # Normalize locale
        locale = args.locale
        if locale == 'pt':
            locale = 'pt_BR'
        elif locale == 'es':
            locale = 'es_AR'

        # Validate input directory
        if not os.path.exists(args.input_dir):
            logger.error(f"Input directory does not exist: {args.input_dir}")
            sys.exit(1)

        logger.info("Starting Recommendation Reporting Application")
        logger.info(f"Locale: {locale}")
        logger.info(f"Input Directory: {args.input_dir}")
        logger.info(f"Output Directory: {args.output_dir}")
        logger.info(f"Max Workers: {args.max_workers}")

        await run_recommendation_reporting(
            locale=locale,
            input_dir=args.input_dir,
            output_dir=args.output_dir,
            max_workers=args.max_workers
        )

        logger.info("Recommendation Reporting Application completed successfully")
    except Exception as e:
        logger.error(f"Recommendation Reporting Application failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
