"""
Campaign Reporting - Layered Architecture Entry Point

This is the new main entry point for the refactored campaign reporting system.
"""
import asyncio
import sys
import os
import argparse

# Add the current directory to the path so we can import modules
sys.path.append(os.path.dirname(__file__))

import logging_config
from campaign_reporting.presentation.main_controller import run_campaign_reporting

# Setup logging
logger = logging_config.setup_logging(__name__)


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Campaign Reporting System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python campaign_reporting_main.py --locale pt_BR --merchants 123,456,789 --sellers_id 111,222,333
  python campaign_reporting_main.py --locale es_AR --merchants 123,456,789
  python campaign_reporting_main.py --locale pt_BR --merchants 123,456
        """
    )

    parser.add_argument(
        '--locale',
        type=str,
        default='pt_BR',
        choices=['pt_BR', 'es_AR', 'pt', 'es'],
        help='Locale for the reports (default: pt_BR). Accepts pt_BR, es_AR, pt, or es'
    )

    parser.add_argument(
        '--merchants',
        type=str,
        required=True,
        help='Comma-separated merchant IDs'
    )

    parser.add_argument(
        '--sellers_id',
        type=str,
        default=None,
        help='Comma-separated seller IDs (optional)'
    )

    return parser.parse_args()


def parse_ids_input(input_str):
    """
    Parse input string as either comma-separated IDs or file path.

    Args:
        input_str: String containing either comma-separated IDs or file path

    Returns:
        List of IDs
    """
    if not input_str:
        return []

    # Check if it's a file path (contains .txt or / or \)
    if '.txt' in input_str or '/' in input_str or '\\' in input_str:
        try:
            with open(input_str, 'r') as f:
                ids = [id_str.strip() for id_str in f.read().split(',')]
                return [id_str for id_str in ids if id_str]  # Filter out empty strings
        except FileNotFoundError:
            logger.error(f"File not found: {input_str}")
            return []
        except Exception as e:
            logger.error(f"Error reading file {input_str}: {e}")
            return []
    else:
        # Treat as comma-separated values
        return [id_str.strip() for id_str in input_str.split(',') if id_str.strip()]


async def main():
    """Main entry point for the campaign reporting application."""
    try:
        # Parse command line arguments
        args = parse_arguments()

        # Normalize locale
        locale = args.locale
        if locale == 'pt':
            locale = 'pt_BR'
        elif locale == 'es':
            locale = 'es_AR'

        # Parse merchant and seller IDs
        merchant_ids = parse_ids_input(args.merchants)
        seller_ids = parse_ids_input(args.sellers_id) if args.sellers_id else []

        if not merchant_ids:
            logger.error("No valid merchant IDs provided")
            sys.exit(1)

        logger.info("Starting Campaign Reporting Application")
        logger.info(f"Locale: {locale}")
        logger.info(f"Merchant IDs: {len(merchant_ids)} merchants")
        logger.info(f"Seller IDs: {len(seller_ids)} sellers")

        await run_campaign_reporting(
            locale=locale,
            merchant_ids=merchant_ids,
            seller_ids=seller_ids
        )

        logger.info("Campaign Reporting Application completed successfully")
    except Exception as e:
        logger.error(f"Campaign Reporting Application failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
