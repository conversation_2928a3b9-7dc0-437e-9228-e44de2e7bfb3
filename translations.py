"""
Centralized translation module for the GoBots project.
This module provides translations for all user-facing text in the application.
"""

class Translations:
    """
    Centralized translation class that provides access to all translations
    for the application.
    """

    # Dictionary of supported locales and their display names
    SUPPORTED_LOCALES = {
        "pt_BR": "Portugu<PERSON>s (Brasil)",
        "es_AR": "Español (LATAM)"
    }

    # Default locale to use when a requested locale is not supported
    DEFAULT_LOCALE = "pt_BR"

    def __init__(self, locale="pt_BR"):
        """
        Initialize the translation system with the specified locale.

        Args:
            locale (str): The locale code to use (e.g., "pt_BR", "es_AR")
        """
        self.set_locale(locale)

    def set_locale(self, locale):
        """
        Set the active locale for translations.

        Args:
            locale (str): The locale code to use (e.g., "pt_BR", "es_AR")
        """
        # If the locale is not supported, use the default locale
        if locale not in self.SUPPORTED_LOCALES:
            # Handle special case for simplified locale codes
            if locale == "pt":
                locale = "pt_BR"
            elif locale == "es":
                locale = "es_AR"
            else:
                locale = self.DEFAULT_LOCALE

        self.locale = locale

    def get(self, key, **kwargs):
        """
        Get a translation for the specified key in the current locale.

        Args:
            key (str): The translation key
            **kwargs: Format parameters for the translation string

        Returns:
            str: The translated string, or the key itself if no translation is found
        """
        # Get the translations for the current locale
        translations = self.get_all_translations().get(self.locale, {})

        # Get the translation for the key, or use the key itself if not found
        translation = translations.get(key, key)

        # Apply format parameters if provided
        if kwargs and isinstance(translation, str):
            try:
                return translation.format(**kwargs)
            except KeyError:
                # If formatting fails, return the unformatted translation
                return translation

        return translation

    def get_all_translations(self):
        """
        Get all translations for all supported locales.

        Returns:
            dict: A dictionary of all translations
        """
        return {
            "pt_BR": {
                # General terms
                "dashboard_title": "Dashboard de Desempenho",
                "campaign_insights": "Insights da Campanha",
                "performance_charts": "Gráficos de Desempenho",
                "currency_symbol": "R$",
                "sales_comparison": "Comparação de Vendas",
                "purchase_experience_problems": "Problemas na Experiência de Compra",
                "main_metrics": "Métricas Principais",
                "lost_impressions": "Impressões Perdidas",
                "attention": "Atenção",
                "tip": "Dica",
                "recommendation": "Recomendação",
                "summary": "Resumo",
                "info": "Informação",
                "product": "Produto",
                "units_sold": "Unidades Vendidas",
                "total_sales": "Vendas Totais",
                "no_products_with_sales": "Não há produtos com vendas para exibir. Todos os produtos da campanha têm zero vendas.",
                "no_sales_problems": "Você não teve vendas com problemas nos últimos 180 dias.",
                "insufficient_data": "Sem dados suficientes",
                "no_data": "Sem dados",
                "excellent": "Excelente",
                "warning": "Atenção",
                "urgent": "Urgente",
                "neutral": "Neutro",
                "success": "Sucesso",
                "danger": "Perigo",
                "secondary": "Secundário",
                "campaign_info": "Informações da Campanha",

                # Campaign info section
                "campaign_title": "Título",
                "campaign_strategy": "Estratégia",
                "daily_budget": "Orçamento Diário",
                "creation_date": "Data de Criação",
                "last_update": "Última Atualização",
                "days_running": "Dias em Execução",
                "campaign_id": "ID da Campanha",

                # Summary cards
                "total_clicks": "Cliques Totais",
                "total_impressions": "Impressões Totais",
                "total_cost": "Custo Total",
                "total_revenue": "Receita Total",
                "avg_ctr": "CTR Média",
                "avg_roas": "ROAS Médio",
                "acos": "ACOS",
                "cvr": "Taxa de Conversão (CVR)",
                "share_of_voice": "Share of Voice",
                "total_units": "Unidades Totais",

                # ACOS section
                "acos_status": "Status do ACOS",
                "acos_current_value": "ACOS atual",
                "acos_benchmark_value": "Benchmark",
                "acos_explanation": "O ACOS (Advertising Cost of Sale) mede quanto você está gastando em publicidade em relação às vendas geradas. Um ACOS menor indica maior eficiência.",
                "acos_targets": "Alvos de ACOS",
                "acos_benchmark": "ACOS Benchmark",
                "acos_target": "ACOS Target",
                "acos_top_search_target": "ACOS Top Search Target",

                # Impression metrics
                "impression_visibility_metrics": "Métricas de Impressões e Visibilidade",
                "impression_statistics": "Estatísticas de Impressão",
                "impression_rate": "Taxa de Impressão",
                "top_impression_rate": "Taxa de Impressão Top",
                "total_lost_impressions": "Total de Impressões Perdidas",
                "impression_rate_explanation": "dos anúncios possíveis foram exibidos",
                "top_impression_explanation": "leilões ganhos nas primeiras posições",
                "lost_impression_explanation": "oportunidades de exibição não aproveitadas",
                "impression_loss_reasons": "Motivos de Perda de Impressões",

                # Chart titles and labels
                "ads_sales_chart_title": "Vendas por Anúncios (R$)",
                "organic_sales_chart_title": "Unidades Vendidas",
                "lost_impressions_chart_title": "Motivos de Impressões Perdidas",
                "no_data_available": "Não há dados de {metric} disponíveis",
                "sales_text": "Vendas",
                "units_text": "Unidades",
                "by_insufficient_budget": "Por Orçamento Insuficiente",
                "by_low_ranking": "Por Classificação Baixa",

                # Lost impressions section
                "campaign_losing": "Sua campanha está perdendo <strong>{total_lost:.1f}%</strong> das impressões possíveis pelos seguintes motivos:",
                "by_insufficient_budget_label": "Por Orçamento Insuficiente:",
                "by_low_ranking_label": "Por Classificação Baixa:",
                "of_losses": "das perdas",
                "impression_rate_current": "A <strong>Taxa de Impressão</strong> atual da sua campanha é de <strong>{impression_share:.1f}%</strong>, o que significa que seus anúncios estão sendo exibidos em {impression_share:.1f}% das oportunidades possíveis.",

                # Optimized campaign section
                "well_optimized_campaign": "Campanha Bem Otimizada",
                "congratulations": "Parabéns! Sua campanha está perdendo apenas <strong>{total_lost:.1f}%</strong> das impressões possíveis, o que indica uma excelente otimização.",
                "maximizing_visibility": "Você está conseguindo maximizar a visibilidade dos seus anúncios, o que geralmente resulta em:",
                "greater_reach": "Maior alcance para seus produtos",
                "better_roi": "Melhor retorno sobre o investimento em publicidade",
                "higher_sales_potential": "Maior potencial de vendas",
                "excellent_work": "Excelente trabalho!",
                "continue_monitoring": "Continue monitorando o desempenho para manter estes ótimos resultados.",

                # Budget recommendation section
                "budget_recommendation": "Recomendação de Orçamento",
                "losing_impressions_budget": "Sua campanha está perdendo <strong>{lost_by_budget:.1f}%</strong> das impressões possíveis devido a um orçamento diário insuficiente.",
                "recommend_increase_budget": "Recomendamos aumentar o orçamento diário atual de <strong>{currency_symbol}{campaign_budget:,.2f}</strong> para melhorar a visibilidade dos seus anúncios.",
                "budget_tip": "Um aumento de 20-30% no orçamento diário pode reduzir significativamente as impressões perdidas e aumentar o alcance da sua campanha.",

                # Ad ranking section
                "ad_ranking": "Classificação dos Anúncios",
                "losing_impressions_ranking": "Sua campanha está perdendo <strong>{lost_by_rank:.1f}%</strong> das impressões possíveis devido à falta ou baixa classificação dos anúncios.",

                # ACOS related
                "acos_status_below": "🏆 Abaixo do benchmark",
                "acos_status_near": "✅ Próximo do benchmark",
                "acos_status_above": "❌ Acima do benchmark",
                "acos_current": "ACOS atual",
                "acos_target_adjust_above": "Recomendamos ajustar o ACOS target para mais próximo do benchmark. Seu ACOS target ({acos_target:.2f}%) está {acos_target_benchmark_diff:.1f}% acima do benchmark ({acos_benchmark:.2f}%).",
                "acos_target_adjust_below": "Recomendamos ajustar o ACOS target para mais próximo do benchmark. Seu ACOS target ({acos_target:.2f}%) está {acos_target_benchmark_diff:.1f}% abaixo do benchmark ({acos_benchmark:.2f}%).",

                # Sales table
                "sales_info": "Os dados abaixo mostram as vendas totais apenas para produtos que tiveram vendas. Produtos com zero vendas não são exibidos.",
                "sales_summary": "Total de unidades vendidas: <strong>{total_units}</strong> | Total de vendas da campanha: <strong>{total_sales}</strong>",

                # Lost impressions
                "insufficient_budget": "Orçamento Insuficiente",
                "low_ranking": "Classificação Baixa",
                "of_total_losses": "das perdas totais",

                # Purchase experience
                "purchase_experience_info": "Os dados abaixo mostram problemas identificados na experiência de compra dos produtos. Resolver estes problemas pode ajudar a melhorar a classificação dos anúncios e reduzir as impressões perdidas.",
                "purchase_experience_tip": "Resolver os problemas listados acima pode melhorar significativamente a classificação dos seus anúncios. Produtos com problemas na experiência de compra têm menor visibilidade nos resultados de busca, o que reduz o potencial de vendas.",
                "overall_rating": "Avaliação Geral",
                "main_problems": "Principais Problemas",
                "unspecified_problem": "Problema não especificado",
                "no_sales_problems_message": "Você não teve vendas com problemas nos últimos 180 dias.",

                # Charts
                "ads_sales_chart": "Vendas por Anúncios",
                "organic_sales_chart": "Vendas Orgânicas",
                "acos_over_time": "ACOS ao Longo do Tempo",
                "ctr_chart": "CTR (Taxa de Cliques)",
                "cpc_chart": "CPC (Custo por Clique)",

                # Detailed sales metrics
                "detailed_sales_metrics": "Métricas Detalhadas de Vendas",
                "organic_sales": "Vendas sem Publicidade (Orgânicas)",
                "ads_sales": "Vendas com Publicidade",
                "direct_sales": "Vendas Diretas",
                "indirect_sales": "Vendas Indiretas",
                "products_quantity": "Quantidade de Produtos",
                "units_quantity": "Quantidade de Unidades",
                "total_value": "Valor Total",
                "total_ads_sales": "Total de Vendas via Anúncios",
                "ad_status": "Status do Anúncio",
                "items_text": "itens",
                "units_text": "unidades",

                # WhatsApp report translations
                "metrics_title": "MÉTRICAS DE PERFORMANCE DA CAMPANHA {campaign_name}",
                "acos_whatsapp": "*ACOS:* Real {acos_real}% | Configurado {acos_target}%",
                "budget_spent": "*Orçamento Gasto:* {cost}",
                "daily_cost": "*Custo Diário:* Real {avg_daily_cost} | Configurado {budget}",
                "total_sales_whatsapp": "*Vendas Totais:* {total_sales} (Ads: {ads_sales} | Orgânicas: {organic_sales})",
                "main_indicators": "INDICADORES PRINCIPAIS",
                "acos_higher_below": "⚠️ ACOS {deviation:.1f}% maior que a meta, mas abaixo do benchmark ({acos_benchmark}%).",
                "acos_higher_above": "⚠️ ACOS {deviation:.1f}% maior que a meta e acima do benchmark ({acos_benchmark}%).",
                "acos_below_benchmark": "✅ ACOS real abaixo da meta e do benchmark ({acos_benchmark}%).",
                "acos_below_near": "✅ ACOS real abaixo da meta, mas próximo do ACOS benchmark ({acos_benchmark}%).",
                "acos_expected": "✅ ACOS dentro do esperado.",
                "budget_above": "⚠️ Orçamento diário acima do configurado",
                "budget_expected": "✅ Orçamento diário dentro do esperado",
                "roas_impressive": "🏆 ROAS Impressionante: {roas:.2f}x",
                "roas_good": "✅ ROAS Bom: {roas:.2f}x",
                "roas_low": "⚠️ ROAS Baixo: {roas:.2f}x",
                "ads_performance": "PERFORMANCE DOS ADS",
                "positive_effect": "Teve Efeito Positivo - {total} vendas totais:",
                "via_ads": "{ad_quantity} ({ad_percentage:.1f}%) via anúncios",
                "organic": "{organic_quantity} ({organic_percentage:.1f}%) orgânicas",
                "cvr": "Tasa de Conversión (CVR) {cvr}%",
                "no_results": "⛔ Sem Resultados Detectáveis",
                "suggestions": "SUGESTÕES",
                "improve_rank": "*Priorizar melhoria de rank de anúncios* - {lost_rank:.1f}% das impressões perdidas por rank.",
                "increase_budget": "*Considere aumentar o budget diário* - {lost_budget:.1f}% das impressões perdidas por orçamento.",
                "adjust_acos_above": "*Considere ajustar o ACOS target para melhorar o ROAS.* - ACOS target ({target_acos:.2f}%) está {acos_difference:.1f}% acima do benchmark ({acos_benchmark:.2f}%).",
                "adjust_acos_below": "*Considere ajustar o ACOS target para melhorar o ROAS.* - ACOS target ({target_acos:.2f}%) está {acos_difference:.1f}% abaixo do benchmark ({acos_benchmark:.2f}%).",
                "no_suggestions": "✅ Nenhuma sugestão necessária. A campanha está dentro dos parâmetros esperados."
            },
            "es_AR": {
                # General terms
                "dashboard_title": "Panel de Rendimiento",
                "campaign_insights": "Insights de la Campaña",
                "performance_charts": "Gráficos de Rendimiento",
                "currency_symbol": "$",
                "sales_comparison": "Comparación de Ventas",
                "purchase_experience_problems": "Problemas en la Experiencia de Compra",
                "main_metrics": "Métricas Principales",
                "lost_impressions": "Impresiones Perdidas",
                "attention": "Atención",
                "tip": "Consejo",
                "recommendation": "Recomendación",
                "summary": "Resumen",
                "info": "Información",
                "product": "Producto",
                "units_sold": "Unidades Vendidas",
                "total_sales": "Ventas Totales",
                "no_products_with_sales": "No hay productos con ventas para mostrar. Todos los productos de la campaña tienen cero ventas.",
                "no_sales_problems": "No has tenido ventas con problemas en los últimos 180 días.",
                "insufficient_data": "Datos insuficientes",
                "no_data": "Sin datos",
                "excellent": "Excelente",
                "warning": "Atención",
                "urgent": "Urgente",
                "neutral": "Neutral",
                "success": "Éxito",
                "danger": "Peligro",
                "secondary": "Secundario",
                "campaign_info": "Información de la Campaña",

                # Campaign info section
                "campaign_title": "Título",
                "campaign_strategy": "Estrategia",
                "daily_budget": "Presupuesto Diario",
                "creation_date": "Fecha de Creación",
                "last_update": "Última Actualización",
                "days_running": "Días en Ejecución",
                "campaign_id": "ID de la Campaña",

                # Summary cards
                "total_clicks": "Clics Totales",
                "total_impressions": "Impresiones Totales",
                "total_cost": "Costo Total",
                "total_revenue": "Ingresos Totales",
                "avg_ctr": "CTR Promedio",
                "avg_roas": "ROAS Promedio",
                "acos": "ACOS",
                "cvr": "CVR (Conversión)",
                "share_of_voice": "Share of Voice",
                "total_units": "Unidades Totales",

                # ACOS section
                "acos_status": "Estado del ACOS",
                "acos_current_value": "ACOS actual",
                "acos_benchmark_value": "Benchmark",
                "acos_explanation": "El ACOS (Advertising Cost of Sale) mide cuánto estás gastando en publicidad en relación con las ventas generadas. Un ACOS menor indica mayor eficiencia.",
                "acos_targets": "Objetivos de ACOS",
                "acos_benchmark": "ACOS Benchmark",
                "acos_target": "ACOS Target",
                "acos_top_search_target": "ACOS Top Search Target",

                # Impression metrics
                "impression_visibility_metrics": "Métricas de Impresiones y Visibilidad",
                "impression_statistics": "Estadísticas de Impresión",
                "impression_rate": "Tasa de Impresión",
                "top_impression_rate": "Tasa de Impresión Top",
                "total_lost_impressions": "Total de Impresiones Perdidas",
                "impression_rate_explanation": "de los anuncios posibles fueron mostrados",
                "top_impression_explanation": "subastas ganadas en las primeras posiciones",
                "lost_impression_explanation": "oportunidades de visualización no aprovechadas",
                "impression_loss_reasons": "Motivos de Pérdida de Impresiones",

                # Chart titles and labels
                "ads_sales_chart_title": "Ventas por Anuncios ($)",
                "organic_sales_chart_title": "Unidades Vendidas",
                "lost_impressions_chart_title": "Motivos de Impresiones Perdidas",
                "no_data_available": "No hay datos de {metric} disponibles",
                "sales_text": "Ventas",
                "units_text": "Unidades",
                "by_insufficient_budget": "Por Presupuesto Insuficiente",
                "by_low_ranking": "Por Clasificación Baja",

                # Lost impressions section
                "campaign_losing": "Tu campaña está perdiendo <strong>{total_lost:.1f}%</strong> de las impresiones posibles por los siguientes motivos:",
                "by_insufficient_budget_label": "Por Presupuesto Insuficiente:",
                "by_low_ranking_label": "Por Clasificación Baja:",
                "of_losses": "de las pérdidas",
                "impression_rate_current": "La <strong>Tasa de Impresión</strong> actual de tu campaña es de <strong>{impression_share:.1f}%</strong>, lo que significa que tus anuncios se están mostrando en el {impression_share:.1f}% de las oportunidades posibles.",

                # Optimized campaign section
                "well_optimized_campaign": "Campaña Bien Optimizada",
                "congratulations": "¡Felicitaciones! Tu campaña está perdiendo solo <strong>{total_lost:.1f}%</strong> de las impresiones posibles, lo que indica una excelente optimización.",
                "maximizing_visibility": "Estás logrando maximizar la visibilidad de tus anuncios, lo que generalmente resulta en:",
                "greater_reach": "Mayor alcance para tus productos",
                "better_roi": "Mejor retorno sobre la inversión en publicidad",
                "higher_sales_potential": "Mayor potencial de ventas",
                "excellent_work": "¡Excelente trabajo!",
                "continue_monitoring": "Continúa monitoreando el rendimiento para mantener estos excelentes resultados.",

                # Budget recommendation section
                "budget_recommendation": "Recomendación de Presupuesto",
                "losing_impressions_budget": "Tu campaña está perdiendo <strong>{lost_by_budget:.1f}%</strong> de las impresiones posibles debido a un presupuesto diario insuficiente.",
                "recommend_increase_budget": "Recomendamos aumentar el presupuesto diario actual de <strong>{currency_symbol}{campaign_budget:,.2f}</strong> para mejorar la visibilidad de tus anuncios.",
                "budget_tip": "Un aumento del 20-30% en el presupuesto diario puede reducir significativamente las impresiones perdidas y aumentar el alcance de tu campaña.",

                # Ad ranking section
                "ad_ranking": "Clasificación de los Anuncios",
                "losing_impressions_ranking": "Tu campaña está perdiendo <strong>{lost_by_rank:.1f}%</strong> de las impresiones posibles debido a la falta o baja clasificación de los anuncios.",

                # ACOS related
                "acos_status_below": "🏆 Por debajo del benchmark",
                "acos_status_near": "✅ Cerca del benchmark",
                "acos_status_above": "❌ Por encima del benchmark",
                "acos_current": "ACOS actual",
                "acos_target_adjust_above": "Recomendamos ajustar el ACOS target para acercarlo al benchmark. Tu ACOS target ({acos_target:.2f}%) está {acos_target_benchmark_diff:.1f}% por encima del benchmark ({acos_benchmark:.2f}%).",
                "acos_target_adjust_below": "Recomendamos ajustar el ACOS target para acercarlo al benchmark. Tu ACOS target ({acos_target:.2f}%) está {acos_target_benchmark_diff:.1f}% por debajo del benchmark ({acos_benchmark:.2f}%).",

                # Sales table
                "sales_info": "Los datos a continuación muestran las ventas totales solo para productos que tuvieron ventas. Los productos con cero ventas no se muestran.",
                "sales_summary": "Total de unidades vendidas: <strong>{total_units}</strong> | Total de ventas de la campaña: <strong>{total_sales}</strong>",

                # Lost impressions
                "insufficient_budget": "Presupuesto Insuficiente",
                "low_ranking": "Clasificación Baja",
                "of_total_losses": "de las pérdidas totales",

                # Purchase experience
                "purchase_experience_info": "Los datos a continuación muestran problemas identificados en la experiencia de compra de los productos. Resolver estos problemas puede ayudar a mejorar la clasificación de los anuncios y reducir las impresiones perdidas.",
                "purchase_experience_tip": "Resolver los problemas enumerados anteriormente puede mejorar significativamente la clasificación de tus anuncios. Los productos con problemas en la experiencia de compra tienen menos visibilidad en los resultados de búsqueda, lo que reduce el potencial de ventas.",
                "overall_rating": "Evaluación General",
                "main_problems": "Problemas Principales",
                "unspecified_problem": "Problema no especificado",
                "no_sales_problems_message": "No has tenido ventas con problemas en los últimos 180 días.",

                # Charts
                "ads_sales_chart": "Ventas por Anuncios",
                "organic_sales_chart": "Ventas Orgánicas",
                "acos_over_time": "ACOS a lo Largo del Tiempo",
                "ctr_chart": "CTR (Tasa de Clics)",
                "cpc_chart": "CPC (Costo por Clic)",

                # Detailed sales metrics
                "detailed_sales_metrics": "Métricas Detalladas de Ventas",
                "organic_sales": "Ventas sin Publicidad (Orgánicas)",
                "ads_sales": "Ventas con Publicidad",
                "direct_sales": "Ventas Directas",
                "indirect_sales": "Ventas Indirectas",
                "products_quantity": "Cantidad de Productos",
                "units_quantity": "Cantidad de Unidades",
                "total_value": "Valor Total",
                "total_ads_sales": "Total de Ventas vía Anuncios",
                "ad_status": "Estado del Anuncio",
                "items_text": "ítems",
                "units_text": "unidades",

                # WhatsApp report translations
                "metrics_title": "MÉTRICAS DE RENDIMIENTO DE LA CAMPAÑA {campaign_name}",
                "acos_whatsapp": "*ACOS:* Real {acos_real}% | Configurado {acos_target}%",
                "budget_spent": "*Presupuesto Gastado:* {cost}",
                "daily_cost": "*Costo Diario:* Real {avg_daily_cost} | Configurado {budget}",
                "total_sales_whatsapp": "*Ventas* Totales:* {total_sales} (Ads: {ads_sales} | Orgánicas: {organic_sales})",
                "main_indicators": "INDICADORES PRINCIPALES",
                "acos_higher_below": "⚠️ ACOS {deviation:.1f}% mayor que la meta, pero por debajo del benchmark ({acos_benchmark}%).",
                "acos_higher_above": "⚠️ ACOS {deviation:.1f}% mayor que la meta y por encima del benchmark ({acos_benchmark}%).",
                "acos_below_benchmark": "✅ ACOS real por debajo de la meta y del benchmark ({acos_benchmark}%).",
                "acos_below_near": "✅ ACOS real por debajo de la meta, pero cerca del ACOS benchmark ({acos_benchmark}%).",
                "acos_expected": "✅ ACOS dentro de lo esperado.",
                "budget_above": "⚠️ Presupuesto diario por encima del configurado",
                "budget_expected": "✅ Presupuesto diario dentro de lo esperado",
                "roas_impressive": "🏆 ROAS Impresionante: {roas:.2f}x",
                "roas_good": "✅ ROAS Bueno: {roas:.2f}x",
                "roas_low": "⚠️ ROAS Bajo: {roas:.2f}x",
                "ads_performance": "RENDIMIENTO DE LOS ANUNCIOS",
                "positive_effect": "Tuvo Efecto Positivo - {total} ventas totales:",
                "via_ads": "{ad_quantity} ({ad_percentage:.1f}%) vía anuncios",
                "organic": "{organic_quantity} ({organic_percentage:.1f}%) orgánicas",
                "cvr": "Tasa de Conversión (CVR) {cvr:.2f}%",
                "no_results": "⛔ Sin Resultados Detectables",
                "suggestions": "SUGERENCIAS",
                "improve_rank": "*Priorizar mejora de ranking de anuncios* - {lost_rank:.1f}% de las impresiones perdidas por ranking.",
                "increase_budget": "*Considere aumentar el presupuesto diario* - {lost_budget:.1f}% de las impresiones perdidas por presupuesto.",
                "adjust_acos_above": "*Considere ajustar el ACOS target para mejorar el ROAS.* - ACOS target ({target_acos:.2f}%) está {acos_difference:.1f}% por encima del benchmark ({acos_benchmark:.2f}%).",
                "adjust_acos_below": "*Considere ajustar el ACOS target para mejorar el ROAS.* - ACOS target ({target_acos:.2f}%) está {acos_difference:.1f}% por debajo del benchmark ({acos_benchmark:.2f}%).",
                "no_suggestions": "✅ No se necesitan sugerencias. La campaña está dentro de los parámetros esperados."
            }
        }

# Create a global instance for easy access
translator = Translations()

def get_translation(key, locale="pt_BR", **kwargs):
    """
    Convenience function to get a translation without creating a Translations instance.

    Args:
        key (str): The translation key
        locale (str): The locale code to use (e.g., "pt_BR", "es_AR")
        **kwargs: Format parameters for the translation string

    Returns:
        str: The translated string
    """
    translator.set_locale(locale)
    return translator.get(key, **kwargs)
